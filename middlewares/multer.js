const multer = require("multer");
const fs = require("fs");
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const user_folder_name = req.user._id.toString();
    const newFolder = `uploads/${user_folder_name}`;
    if (!fs.existsSync(newFolder)) {
      fs.mkdirSync(newFolder);
    }
    req.body.newFolder = newFolder;
    cb(null, newFolder);
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname.trim());
  },
});
module.exports = multer({
  storage: storage,
  limits: { fileSize: 100 * 1024 * 1024, fieldSize: 8 * 1024 * 1024 },
});
