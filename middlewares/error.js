const multer = require("multer");

const ErrorHandler = (err, req, res, next) => {
  const isDevEnv = process.env.NODE_ENV === "dev";

  if (err instanceof multer.MulterError) {
    if (err.code === "LIMIT_FILE_SIZE") {
      return res.status(413).json({
        success: false,
        status: 413,
        message: `File${
          err.field ? ` '${err.field}'` : ""
        } is too large. Max allowed size is 100 MB.`,
        stack: isDevEnv ? err.stack || "" : {},
      });
    }

    if (err.code === "LIMIT_FIELD_SIZE") {
      return res.status(413).json({
        success: false,
        status: 413,
        message: `Form field '${err.field}' too large. Max allowed size is 8 MB.`,
        stack: isDevEnv ? err.stack || "" : {},
      });
    }

    return res.status(400).json({
      success: false,
      status: 400,
      message: `Upload error: ${err.message}`,
      stack: isDevEnv ? err.stack || "" : {},
    });
  }

  const msg = isDevEnv ? err.message : "Something went wrong";
  const statusCode = err.statusCode || 500;

  console.error(err.stack || err);
  res.status(statusCode).json({
    success: false,
    status: statusCode,
    message: msg,
    stack: isDevEnv ? err.stack || "" : {},
  });
};

module.exports = ErrorHandler;
