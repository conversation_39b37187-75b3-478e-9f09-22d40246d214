const jwt = require("jsonwebtoken");
const User = require("../models/user.model");

const auth = async (req, res, next) => {
  try {
    const authToken = req.headers["authorization"];
    const token = authToken && authToken.split(" ")[1];
    if (!token) return res.status(403).send({ error: "No token provided." });
    const data = jwt.verify(token, process.env.JWT_KEY);
    const user = await User.findOne(
      { _id: data.id, "tokens.token": token },
      { tokens: 0 }
    );
    if (!user) return res.status(403).send({ error: "User not found" });

    req.user = user;
    req.data = data;
    req.token = token;
    next();
  } catch (error) {
    console.log(error);
    res.status(401).send({ error: "Not authorized to access this resource" });
  }
};

module.exports = auth;
