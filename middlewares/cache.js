function hashString(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    hash = ((hash << 5) - hash + charCode) | 0;
  }
  return hash + 2147483647 + 1;
}

class MemoryCache {
  constructor() {
    this.cache = {};
    this.dependencies = {};
  }

  get(key, depArrayValues) {
    const item = this.cache[key];
    const checkDepsChanged = this.dependenciesChanged(key, depArrayValues);

    if (checkDepsChanged) {
      if (item && item.timer) {
        clearInterval(item.timer);
      }
      delete this.cache[key];
      return null;
    }

    if (item && (!item.expireTime || item.expireTime > Date.now())) {
      return item.value;
    } else {
      delete this.cache[key];
      return null;
    }
  }

  set(key, value, timeoutMs, callback, dependencies) {
    if (timeoutMs && timeoutMs > 0) {
      const expireTime = Date.now() + timeoutMs;
      this.cache[key] = { value, expireTime, dependencies, timeoutMs };

      if (callback) {
        this.cache[key].timer = setTimeout(() => {
          if (this.cache[key]) {
            callback(key, this.cache[key].value);
            delete this.cache[key];
          }
        }, timeoutMs);
      }
    } else {
      this.cache[key] = { value, dependencies };
    }

    this.dependencies[key] = dependencies;
  }

  remove(key) {
    delete this.cache[key];
    delete this.dependencies[key];
  }

  has(key) {
    return key in this.cache;
  }

  dependenciesChanged(key, depArrayValues) {
    const dependencies = this.dependencies[key];

    if (!dependencies) {
      return false;
    }

    const check =
      JSON.stringify(dependencies) === JSON.stringify(depArrayValues);

    if (check) {
      return false;
    } else {
      this.dependencies[key] = depArrayValues;
      return true;
    }
  }
}

const cache = new MemoryCache();

function expressCache(opts = {}) {
  const defaults = {
    dependsOn: () => [],
    timeOut: 60 * 60 * 1000,
    onTimeout: () => {
      console.log("Cache removed");
    },
    onCacheMiss: () => {},
    onCacheServed: () => {},
    onCacheStored: () => {},
  };

  const options = {
    ...defaults,
    ...opts,
  };

  const {
    dependsOn,
    timeOut,
    onTimeout,
    onCacheMiss,
    onCacheServed,
    onCacheStored,
  } = options;

  return function (req, res, next) {
    const cacheUrl = req.originalUrl || req.url;
    const cacheKey = "c_" + hashString(cacheUrl);
    const depArrayValues = dependsOn();

    const cachedResponse = cache.get(cacheKey, depArrayValues);

    if (cachedResponse) {
      const cachedBody = cachedResponse.body;
      const cachedHeaders = cachedResponse.headers;
      const cachedStatusCode = cachedResponse.statusCode;

      if (cachedHeaders) {
        res.set(JSON.parse(cachedHeaders));
      }

      if (typeof cachedBody === "string") {
        try {
          const jsonData = JSON.parse(cachedBody);
          onCacheServed(cacheUrl);
          res.status(cachedStatusCode).json(jsonData);
        } catch (error) {
          onCacheServed(cacheUrl);
          res.status(cachedStatusCode).send(cachedBody);
        }
      } else {
        onCacheServed(cacheUrl);
        res.status(cachedStatusCode).send(cachedBody);
      }
    } else {
      onCacheMiss(cacheUrl);
      const originalSend = res.send;
      const originalJson = res.json;

      res.send = function (body) {
        cache.set(
          cacheKey,
          {
            body: typeof body === "object" ? JSON.stringify(body) : body,
            headers: JSON.stringify(res.getHeaders()),
            statusCode: res.statusCode,
          },
          timeOut,
          onTimeout,
          depArrayValues
        );
        onCacheStored(cacheUrl);
        originalSend.call(this, body);
      };

      res.json = function (body) {
        cache.set(
          cacheKey,
          {
            body: body,
            headers: JSON.stringify(res.getHeaders()),
            statusCode: res.statusCode,
          },
          timeOut,
          onTimeout,
          depArrayValues
        );
        onCacheStored(cacheUrl);
        originalJson.call(this, body);
      };

      next();
    }
  };
}
module.exports = expressCache;
