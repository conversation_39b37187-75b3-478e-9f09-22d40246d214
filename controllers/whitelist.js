require("dotenv").config();
const { workerD<PERSON>, Worker } = require("node:worker_threads");
const { MerkleTree } = require("merkletreejs");
const keccak256 = require("keccak256");
const os = require("os");
const { u64 } = require("@saberhq/token-utils");
const WhitelistEntry = require("../models/whitelistEntries.model");
const {
  readWhitelistEntries,
  countWhitelistEntries,
  readWhitelistEntry,
  removeWhitelistEntry,
} = require("../services/whitelist.service");
const { RedisCache } = require("../cache/main");
const {
  countCollections,
  readCollection,
} = require("../services/collection.service");
const WorkerPool = require("../workers/worker_pool.js");
const { NODE_ENV, REDIS_URL } = process.env;
const Bull = require("bull");
const { sleep } = require("../utils/sleep.js");
const {
  createOrUpdateProof,
  getProof,
  deleteProofs,
} = require("../services/proof.service.js");
const { getCoinDecimals } = require("../utils/getDecimalsFromCoin.js");
const cache = new RedisCache(5);
const pool = new WorkerPool(os.cpus().length);

let breakMassProof = false;
const to_buf = (account, amount, price) => {
  return Buffer.concat([
    Buffer.from(account.slice(2), "hex"),
    new u64(amount).toArrayLike(Buffer, "le", 8),
    new u64(price).toArrayLike(Buffer, "le", 8),
  ]);
};

function findObjectById(array, id) {
  return array.find((item) => item.id === id);
}

exports.root = async (req, res, next) => {
  let { collection_id } = req.body;
  const filter = { collection_id };
  breakMassProof = true;
  await deleteProofs({ collection_id });
  const phaseDetails = await readCollection(
    { _id: collection_id },
    { phases: 1, seed: 1 }
  );
  const phases = phaseDetails.phases;
  const phaseIds = phases.map((item) => item.id);
  whitelistAddresses = [];
  for (let i = 0; i < phaseIds.length; i++) {
    const ids = phaseIds.slice(0, i + 1);
    let wlAddresses = await readWhitelistEntries(filter, ids);
    wlAddresses.forEach((item) => {
      if (item.mint_limit > 0) {
        item.phase = phaseIds[i];
        item.wallet_address = item._id;
        whitelistAddresses.push(item);
      }
    });
  }

  setTimeout(() => {
    calculateMassProof(
      whitelistAddresses,
      collection_id,
      phases,
      20,
      phaseDetails.seed.coin_type
    );
    console.log("Mass proof calculation started.");
  }, 1000);

  let WhitelistAddressesBuff = [];
  const decimal = getCoinDecimals(phaseDetails.seed.coin_type);
  for (let i = 0; i < whitelistAddresses.length; i++) {
    whitelistAddresses[i];
    const phaseObj = findObjectById(phases, whitelistAddresses[i].phase);
    WhitelistAddressesBuff.push(
      to_buf(
        whitelistAddresses[i].wallet_address, //wallet_address
        whitelistAddresses[i].mint_limit, //mint_limit
        Number(phaseObj.mint_price * 10 ** decimal) //mint_price
      )
    );
  }

  let leafNodes = WhitelistAddressesBuff.map((address) => keccak256(address));
  let tree = new MerkleTree(leafNodes, keccak256, { sortPairs: true });
  const root = tree.getRoot();
  await cache.flush();
  res.json({ root });
};

exports.proof = async (req, res) => {
  const { wallet_address, collection_id, phase } = req.query;
  const filter = { wallet_address, collection_id, phase: phase ? phase : null };
  const mint_limit_key = `ml_${collection_id}${wallet_address}${phase}`;
  const exists = await cache.get(mint_limit_key, () =>
    countWhitelistEntries(filter)
  );

  if (!exists)
    return res.status(400).json({
      msg: "Whitelist entry associated with this wallet address not found.",
    });
  const proofArr = await getProof({ phase, wallet_address, collection_id });

  const collection_key = `collection_${collection_id}`;
  const phaseDetails = await cache.get(collection_key, () =>
    readCollection({ _id: collection_id }, { phases: 1, seed: 1 })
  );
  if (!phaseDetails)
    return res.status(400).json({ msg: "Collection not found" });
  const phases = phaseDetails.phases;

  const currentPhase = findObjectById(phases, phase);
  const currentPhaseTime = new Date(currentPhase.mint_time);

  if (new Date() < currentPhaseTime && NODE_ENV == "prod")
    return res.status(400).json({ msg: "Sale not started!" });

  if (proofArr) return res.status(200).json({ data: proofArr.proof });

  whitelistAddresses = [];
  const phaseIds = phases.map((item) => item.id);

  for (let i = 0; i < phaseIds.length; i++) {
    const ids = phaseIds.slice(0, i + 1);
    const key = `${collection_id}${phases[i].id}`;
    let wlAddresses = await cache.get(key, () =>
      readWhitelistEntries({ collection_id }, ids)
    );
    wlAddresses.forEach((item) => {
      if (item.mint_limit > 0) {
        item.phase = phaseIds[i];
        item.wallet_address = item._id;
        whitelistAddresses.push(item);
      }
    });
  }

  pool.runTask(
    {
      whitelistAddresses,
      wallet_address,
      phases,
      phase,
      coin_type: phaseDetails.seed.coin_type,
    },
    (err, result) => {
      if (err) {
        console.log(err);
      }
      return res.status(200).json({ data: result });
    }
  );
  return;
};

async function calculateMassProof(
  whitelistAddresses,
  collection_id,
  phases,
  batchSize = 20,
  coin_type
) {
  let calculatedCount = 1;
  breakMassProof = false;

  const processAddress = async (wlAddress) => {
    if (breakMassProof) {
      console.log("Mass proof calculation stopped.");
      return;
    }

    const { wallet_address, phase } = wlAddress;

    try {
      const result = await new Promise((resolve, reject) => {
        pool.runTask(
          { whitelistAddresses, wallet_address, phases, phase, coin_type },
          (err, result) => {
            if (err) {
              reject(err);
            } else {
              resolve(result);
            }
          }
        );
      });

      if (result.length > 15) {
        createOrUpdateProof({
          wallet_address,
          phase,
          collection_id,
          proof: result,
        }).catch((error) => {
          console.error(`Error saving proof for ${wallet_address}:`, error);
        });
      }

      calculatedCount++;
      console.log("calculatedCount::: ", calculatedCount);
    } catch (error) {
      console.error(`Error processing ${wallet_address}:`, error);
    }
  };

  for (let i = 0; i < whitelistAddresses.length; i += batchSize) {
    if (breakMassProof) {
      console.log("Mass proof calculation stopped.");
      break;
    }

    const batch = whitelistAddresses.slice(i, i + batchSize);
    await Promise.allSettled(batch.map(processAddress));
  }

  console.log("Mass proof calculation completed.");
}

exports.getMintLimit = async (req, res) => {
  const { collection_id, wallet_address, phase } = req.query;

  const key = `${phase}${collection_id}${wallet_address}`;
  let data = await cache.get(key, () =>
    readWhitelistEntry(
      { collection_id, wallet_address, phase: phase ? phase : null },
      { mint_limit: 1, _id: 0 }
    )
  );

  res.status(200).json({ data });
};

exports.deleteWhitelistEntry = async (req, res) => {
  const { collection_id, wallet_address, phase } = req.query;
  const { _id } = req.user;
  const exists = await countCollections({ _id: collection_id, user_id: _id });
  if (!exists)
    return res
      .status(400)
      .json({ msg: "Requested resource is not associated with the user." });
  const data = await removeWhitelistEntry({
    collection_id,
    wallet_address,
    phase: phase ? phase : null,
  });
  res.status(200).json({ msg: "Requested resource removed.", data });
};

exports.clearCache = async (req, res) => {
  await cache.flush();

  res.status(200).json({ msg: "All data flushed" });
};

exports.deleteOldProofs = async () => {
  try {
    let now = new Date();
    let date = now - 1000 * 60 * 60 * 24 * 10; // 10 days ago
    const filter = { updated_at: { $lt: date } };
    const deleted = await deleteProofs(filter);
  } catch (error) {
    console.log("Error while deleting: ", error);
  }
};
