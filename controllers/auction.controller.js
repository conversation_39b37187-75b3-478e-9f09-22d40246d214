const Auction = require("../models/auction.model");
const {
  readAuctions,
  countAuctions,
  placeBidService,
  isValidBid,
  readAuction,
  removeAuction,
  readBidsOfUser,
} = require("../services/auction.service");
const { pagination } = require("../utils/pagination");
const { createUniqueName } = require("../validations/auction.validation");

exports.createAuction = async (req, res) => {
  try {
    const {
      nft,
      startAt,
      endAt,
      min_bid,
      auction_name,
      image,
      tweet,
      id,
      contract,
      bid_inc,
      coin_type,
      twitter,
      instagram,
    } = req.body;

    const auction = new Auction({
      user_id: req.user._id,
      nft,
      auction_name: await createUniqueName(auction_name),
      startAt,
      endAt,
      image,
      tweet,
      min_bid,
      id,
      contract,
      bid_inc,
      coin_type,
      twitter,
      instagram,
    });
    await auction.save();
    res
      .status(200)
      .json({ success: true, msg: "New auction created", auction });
  } catch (error) {
    next(error);
  }
};

exports.getAuction = async (req, res) => {
  const { auction_name } = req.params;
  const auction = await readAuction({ auction_name: auction_name });
  res.status(200).json({ success: true, msg: "Auction fetched.", auction });
};
exports.getAuctionsOfUser = async (req, res) => {
  const { _id } = req.user;
  const user_id = req.query.user_id || _id;
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const total = await countAuctions({ user_id });
  const auctions = await readAuctions(
    getLimit,
    skip,
    { user_id },
    { biddings: 0 }
  );
  res.status(200).json({
    success: true,
    msg: "Auctions fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    auctions,
  });
};

exports.getAuctions = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const total = await countAuctions({});
  const auctions = await readAuctions(getLimit, skip, {}, {}, { _id: -1 });
  res.status(200).json({
    success: true,
    msg: "Auctions fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    auctions,
  });
};

exports.getLiveAndUpcomingAuctions = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = { endAt: { $gt: Date.now() } };
  const total = await countAuctions(filter);
  const auctions = await readAuctions(getLimit, skip, filter, {}, { endAt: 1 });
  res.status(200).json({
    success: true,
    msg: "Auctions fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    auctions,
  });
};

exports.setAuctionCompleted = async (req, res) => {
  const { id } = req.params;
  const auction = await Auction.findOneAndUpdate(
    { _id: id },
    { $set: { completed: true } }
  );
  res.status(200).json({
    success: true,
    msg: "Auctions updated.",
    auction,
  });
};

exports.getEndedAuctions = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = { endAt: { $lt: Date.now() } };
  const total = await countAuctions(filter);
  const auctions = await readAuctions(
    getLimit,
    skip,
    filter,
    {},
    { endAt: -1 }
  );
  res.status(200).json({
    success: true,
    msg: "Auctions fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    auctions,
  });
};

exports.getBidsOfUser = async (req, res) => {
  // let { _id } = req.user;
  const { wallet_address } = req.query;
  const { auction_id } = req.query;
  const bids = await readBidsOfUser(auction_id, wallet_address);
  res.status(200).json({ bids });
};

exports.placeBid = async (req, res) => {
  const { bid, auction_id, wallet_address, creation_number } = req.body;
  const newbid = {
    wallet_address,
    bid: Number(bid),
    creation_number,
    time: Date.now(),
  };
  const isValid = await isValidBid(auction_id, newbid);
  if (!isValid) return res.status(400).json({ msg: "Place higher bids." });
  const newBid = await placeBidService(auction_id, newbid);
  res.status(200).json({ success: true, msg: `${bid}APT bid placed.`, newBid });
};

exports.deleteAuction = async (req, res) => {
  const { id } = req.params;
  const { _id } = req.user;
  const auctionExists = await countAuctions({ _id: id, user_id: _id });
  if (!auctionExists) return res.status(400).json({ msg: "Invalid action" });
  const auction = await removeAuction({ _id: id, user_id: _id });
  res.status(200).json({ success: true, msg: `Auction removed.`, auction });
};
exports.checkIfNFTExists = async (req, res) => {
  const { name } = req.query;
  const exists = await Auction.findOne(
    { "nft.meta.name": name },
    { "nft.meta": 1 }
  );

  res.status(200).json({ exists: exists ? true : false });
};

exports.getFeaturedAuction = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const auctions = await readAuctions(getLimit, skip, {
    isApproved: true,
    isFeatured: true,
  });
  res.status(200).json({ auctions });
};

exports.getUnapprovedAuctionsOfUser = async (req, res) => {
  const { limit, page } = req.query;
  const { _id } = req.user;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const auctions = await readAuctions(getLimit, skip, {
    user_id: _id,
    isApproved: false,
  });
  res.status(200).json({ auctions });
};

exports.getAuctionStats = async (req, res) => {
  const aggregationPipeline = [
    {
      $group: {
        _id: null,
        running: {
          $sum: { $cond: [{ $eq: ["$completed", false] }, 1, 0] },
        },
        completed: {
          $sum: { $cond: [{ $eq: ["$completed", true] }, 1, 0] },
        },
        total: {
          $sum: { $cond: [{ $eq: ["$isApproved", true] }, 1, 0] },
        },
      },
    },
    {
      $project: {
        _id: 0,
      },
    },
  ];
  const auctions = await Auction.aggregate(aggregationPipeline);

  res.status(200).json({ data: auctions });
};
