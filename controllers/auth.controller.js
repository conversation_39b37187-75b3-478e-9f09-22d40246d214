// const { WalletCore } = require("@aptos-labs/wallet-adapter-core");
// const a = require("aptos-wallet-adapter");
var nacl = require("tweetnacl");
nacl.util = require("tweetnacl-util");
const BN = require("bn.js");
const { stripHexPrefix } = require("ethereumjs-util");
const User = require("../models/user.model");
exports.login = async (req, res) => {
  try {
    const { message, wallet_address, signature, publicKey } = req.body;
    const pubkey = new BN(stripHexPrefix(publicKey), 16);
    let messageBytes = nacl.util.decodeBase64(message);
    const signatureBytes = new BN(stripHexPrefix(signature), 16);

    const verified = nacl.sign.detached.verify(
      messageBytes,
      signatureBytes.toBuffer(),
      pubkey.toBuffer()
    );
    console.log(verified);
    if (!verified) return res.status(500).json({ msg: "Verification failed." });
    const user = await User.findOneAndUpdate(
      { wallet_address },
      { signed: true },
      { upsert: true, new: true }
    );
    console.log(user);
    const token = await user.generateAuthToken();
    return res.json({ verified: verified, token: `Bearer ${token}`, user });
  } catch (error) {
    console.error(error);
    return res.json({ msg: "Login failed!" });
  }
};

exports.register = async (req, res) => {
  const { wallet_address } = req.body;
  const exists = await User.countDocuments({ wallet_address });
  let user;
  if (!exists) {
    const newUser = new User({ wallet_address });
    await newUser.save();
    user = newUser;
  }
  return res.json({ msg: "New user registered!", exists, user });
};

exports.getUsersStats = async (req, res) => {
  const aggregationPipeline = [
    {
      $group: {
        _id: null,
        inside_studio: {
          $sum: { $cond: [{ $eq: ["$signed", true] }, 1, 0] },
        },
        outside_studio: {
          $sum: { $cond: [{ $eq: ["$signed", false] }, 1, 0] },
        },
        total: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
      },
    },
  ];

  const userCounts = await User.aggregate(aggregationPipeline);

  return res.json({ msg: "Users stats fetched!", data: userCounts });
};
