const Verification = require("../models/verification.model");

exports.createVerification = async (req, res) => {
  const {
    full_name,
    image,
    dob,
    gender,
    occupation,
    email,
    residential_address,
    permanent_address,
    document,
  } = req.body;
  const { _id } = req.user;
  const newVerification = new Verification({
    user_id: _id,
    full_name,
    image,
    dob,
    gender,
    occupation,
    email,
    residential_address,
    permanent_address,
    document,
  });
  await newVerification.save().then((verification) => {
    res
      .status(200)
      .json({ success: true, msg: "Verification added.", verification });
  });
  return;
};

exports.updateVerification = async (req, res) => {
  const { id } = req.params;
  const { _id } = req.user;
  const {
    full_name,
    image,
    dob,
    gender,
    occupation,
    email,
    residential_address,
    permanent_address,
    document,
  } = req.body;
  const updated = await Verification.findOneAndUpdate(
    { _id: id, user_id: _id },
    {
      full_name,
      image,
      dob,
      gender,
      occupation,
      email,
      residential_address,
      permanent_address,
      document,
    },
    { new: true }
  );
  res
    .status(200)
    .json({ success: true, msg: "Verification updated.", updated });
};
