const Whitelist = require("../models/whitelist.model");
const WhitelistEntry = require("../models/whitelistEntries.model");
const Collection = require("../models/collection.model");

const {
  readWhitelists,
  countWhitelists,
} = require("../services/whitelist.service");
const { pagination } = require("../utils/pagination");
const { getNewAptosAccount } = require("../helpers/aptos");

exports.createWhitelist = async (req, res) => {
  const {
    collection_id,
    twitter,
    discord_server_name,
    discord_server_id,
    discord_server_url,
    discord_roles,
    whitelist_spots,
    whitelist_start,
    whitelist_end,
  } = req.body;
  const user_id = req.user._id;

  const newWhitelist = new Whitelist({
    user_id,
    collection_id,
    twitter,
    discord_server_name,
    discord_server_id,
    discord_server_url,
    discord_roles,
    whitelist_spots,
    whitelist_start,
    whitelist_end,
  });
  await newWhitelist.save();
  res
    .status(200)
    .json({ success: true, msg: "New whitelist registered.", newWhitelist });
};

exports.createWhitelistEntry = async (req, res) => {
  const {
    collection_id,
    twitter,
    discord,
    wallet_address,
    mint_limit,
    date,
    phase,
  } = req.body;
  const exists = await WhitelistEntry.countDocuments({
    phase,
    collection_id,
    wallet_address,
  });
  if (exists) return res.status(400).json({ msg: "Duplicate entry." });
  const newWhitelistEntry = new WhitelistEntry({
    collection_id,
    twitter,
    discord,
    wallet_address,
    mint_limit,
    phase,
    date,
  });
  await newWhitelistEntry.save().then((entry) => {
    res
      .status(200)
      .json({ success: true, msg: "New whitelist entry registered.", entry });
  });
  return;
};

exports.getWhitelistEntries = async (req, res) => {
  const { collection_id, phase, page, limit } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);

  const spotsCount = await WhitelistEntry.countDocuments({
    phase,
    collection_id,
  });
  const whitelistEntries = await WhitelistEntry.find({ phase, collection_id })
    .sort({ _id: -1 })
    .limit(getLimit)
    .skip(skip);
  res.status(200).json({
    success: true,
    msg: "Whitelist entries fetched.",
    page: getPage,
    perPage: getLimit,
    spotsCount,
    whitelistEntries,
  });
};

exports.searchWhitelistEntries = async (req, res) => {
  const { collection_id, phase, q, page, limit } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);

  const whitelistEntries = await WhitelistEntry.find({
    collection_id,
    phase,
    wallet_address: { $regex: q, $options: "ix" },
  })
    .sort({ _id: -1 })
    .limit(getLimit)
    .skip(skip);
  res.status(200).json({
    success: true,
    msg: "Whitelist entries fetched.",
    page: getPage,
    perPage: getLimit,
    whitelistEntries,
  });
};

exports.getWhitelist = async (req, res) => {
  const { id } = req.params;
  const whitelist = await Whitelist.findById(id);
  res.status(200).json({ success: true, msg: "Whitelist fetched.", whitelist });
};

exports.getAllWhitelists = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const whitelists = await readWhitelists(getLimit, skip, {});
  const total = await countWhitelists({});
  res.status(200).json({
    success: true,
    msg: "Whitelists fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    whitelists,
  });
};
exports.getAllWhitelistsOfUser = async (req, res) => {
  const { limit, page, user_id } = req.query;
  const { getLimit, skip } = pagination(page, limit);
  const whitelists = await readWhitelists(getLimit, skip, { user_id });
  const total = await countWhitelists({ user_id });
  res.status(200).json({
    success: true,
    msg: "Whitelists fetched.",
    page: Number(page),
    perPage: Number(limit),
    total,
    whitelists,
  });
};

exports.getWhitelistFromCollectionUsername = async (req, res) => {
  const { un } = req.params;
  await Collection.findOne({ username: un }, { _id: 1 }).then(
    async (collection) => {
      console.log(collection);
      const whitelist = await Whitelist.findOne({
        collection_id: collection._id,
      });
      res
        .status(200)
        .json({ success: true, msg: "Whitelists fetched.", whitelist });
    }
  );
  return;
};

/**
 *
 *    FOR CRON JOB
 */
exports.findNewWhitelistsToInsertAddresses = async () => {
  const whitelists = await Whitelist.find(
    {
      whitelist_start: { $lt: Date.now() },
      collection_id: { $ne: "647f97c8782416944a3edd50" },
    },
    { collection_id: 1, whitelist_spots: 1, _id: 0 }
  );
  let newWl = [];
  for (w of whitelists) {
    const count = await WhitelistEntry.countDocuments({
      collection_id: w.collection_id,
    });
    w = { whitelist: w, entries: count };
    newWl.push(w);
  }
  const filteredWhitelists = newWl.filter((white) => {
    return white.whitelist.whitelist_spots > white.entries;
  });

  // console.log(filteredWhitelists);
  return filteredWhitelists;
};

exports.insertAddress = async () => {
  const address = getNewAptosAccount();
  const whitelists = await this.findNewWhitelistsToInsertAddresses();

  for (whitelist of whitelists) {
    const newEntry = new WhitelistEntry({
      collection_id: whitelist.whitelist.collection_id,
      wallet_address: address,
      phase: "whitelist",
      mint_limit: 1,
    });
    await newEntry.save();
    console.log("newEntry saved!\n", newEntry);
  }
};
