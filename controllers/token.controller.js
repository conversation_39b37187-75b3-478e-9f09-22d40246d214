const Token = require("../models/token.model");
const { generateRandomString } = require("../utils/stringOperations");

exports.createToken = async (req, res) => {
  const { number } = req.body;

  let inviteCodes = [];
  for (let i = 0; i < number; i++) {
    inviteCodes.push({ token: generateRandomString(16) });
  }
  const tokens = await Token.insertMany(inviteCodes);

  res
    .status(200)
    .json({ success: true, msg: `${number} tokens generated.`, tokens });
};

exports.verifyToken = async (req, res) => {
  const { token } = req.body;
  const verifyToken = await Token.findOneAndUpdate(
    { token, used: false },
    { used: true }
  );
  verifyToken
    ? res.status(200).json({ verified: true })
    : res.status(200).json({ verified: false });
};
