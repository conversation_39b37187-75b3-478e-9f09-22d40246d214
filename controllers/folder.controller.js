const fs = require("fs");
const path = require("path");
const Folder = require("../models/folder.model");
const { pagination } = require("../utils/pagination");
const getIds = require("../utils/readArkb");
const Metadata = require("../models/metadata.model");
const { readFolders, countFolders } = require("../services/folder.service");

exports.createFolder = async (req, res) => {
  // #swagger.tags = ['Folder']
  // #swagger.summary = 'Create folder[info].'
  const { folder_name } = req.body;
  const user_id = req.user._id;
  const pattern = /^[a-zA-Z0-9]+$/;

  function isValid(str) {
    return pattern.test(str);
  }
  if (!isValid(folder_name))
    return res
      .status(400)
      .json({ msg: "Name can contain letters and numbers only." });
  const count = await Folder.countDocuments({ folder_name, user_id });
  if (count)
    return res.status(403).json({
      msg: "You already have a folder with that name, choose a different name.",
    });

  const folderInfo = new Folder({
    user_id,
    folder_name,
  });
  await folderInfo.save();
  res.status(200).json({ folderInfo });
};

exports.getFolderInfo = async (req, res) => {
  // #swagger.tags = ['Folder']
  // #swagger.summary = 'Get a single folder info from database.'
  const { id } = req.params;
  const folderInfo = await Folder.findOne({ _id: id });
  res.status(200).json({ folderInfo });
};
exports.getFoldersOfUser = async (req, res) => {
  // #swagger.tags = ['Folder']
  // #swagger.summary = 'Get all folders of a user from database.'

  let { limit, page } = req.query;
  const user_id = req.user._id;

  const { getLimit, skip, getPage } = pagination(page, limit);
  const total = await countFolders({ user_id });
  const folderInfo = await readFolders(
    getLimit,
    skip,
    { user_id },
    {
      user_id: 1,
      folder_name: 1,
      assets: {
        $cond: {
          if: { $isArray: "$assets.files" },
          then: { $size: "$assets.files" },
          else: 0,
        },
      },
      assetsBaseURI: "$assets.baseURI",
      metadata: {
        $cond: {
          if: { $isArray: "$metadata.files" },
          then: { $size: "$metadata.files" },
          else: 0,
        },
      },
      metadataBaseURI: "$metadata.baseURI",
    }
  );

  res.status(200).json({
    page: getPage,
    perPage: getLimit,
    total,
    folderInfo,
  });
};
exports.getFolderOfUser = async (req, res) => {
  // #swagger.tags = ['Folder']
  // #swagger.summary = 'Get a single folder info of a user from database.'
  const { user_id, folder_name } = req.query;
  const folderInfo = await Folder.find({ user_id, folder_name });
  res.status(200).json({ folderInfo });
};
exports.updateFolderInfo = async (req, res) => {
  // #swagger.tags = ['Folder']
  // #swagger.summary = 'Update a single folder info.'
  const { id } = req.params;
  const { _id } = req.user;
  const { folder_name } = req.body;
  const folderInfo = await Folder.findOneAndUpdate(
    { _id: id, user_id: _id },
    { folder_name },
    { new: true }
  );
  res.status(200).json({ msg: "Updated Successfuly.", folderInfo });
};

const updateAssets = async (user_id, folder_name, folder, ext) => {
  const { ids, url } = getIds(folder);
  console.log(ids, url);

  const uploaded = await Folder.findOneAndUpdate(
    { user_id, folder_name },
    {
      "assets.baseURI": url,
      "assets.ext": ext,
      $push: { "assets.files": { $each: ids } },
    }
  );
  return { success: true, msg: `${ids.length} files uploaded`, uploaded };
};

const updateMetadata = async (user_id, folder_name, folder, baseURI) => {
  const { ids, url } = getIds(folder);

  const uploaded = await Folder.findOneAndUpdate(
    { user_id, folder_name },
    {
      "metadata.baseURI": url,
      $push: { "metadata.files": { $each: ids } },
    }
  );
  return { success: true, msg: `${ids.length} files uploaded`, uploaded };
};

const updateImagesForVideo = async (
  user_id,
  folder_name,
  folder,
  // baseURI,
  ext
) => {
  const { ids, url } = getIds(folder);

  const uploaded = await Folder.findOneAndUpdate(
    { user_id, folder_name },
    {
      "images.baseURI": url,
      "images.ext": ext,
      $push: { "images.files": { $each: ids } },
    }
  );
  return { success: true, msg: `${ids.length} files uploaded`, uploaded };
};

exports.postDeploymentOperation = async (
  type,
  user_id,
  folder_name,
  folder
) => {
  if (type == "assets") {
    const files = fs.readdirSync(`uploads/${folder}`);
    const ext = path.extname(files[0]);
    return await updateAssets(user_id, folder_name, folder, ext);
  }

  if (type == "metadata") {
    return await updateMetadata(user_id, folder_name, folder);
  }
  if (type == "images") {
    const files = fs.readdirSync(`uploads/${folder}`);
    const ext = path.extname(files[0]);
    return await updateImagesForVideo(
      user_id,
      folder_name,
      folder,
      // baseURI,
      ext
    );
  }
};

exports.deleteFolderInfo = async (req, res) => {
  // #swagger.tags = ['Folder']
  // #swagger.summary = 'Delete single folder info from database.'
  const { id } = req.params;
  const { _id } = req.user;
  const folderInfo = await Folder.findOneAndDelete({
    _id: id,
    user_id: _id,
  });
  res.status(200).json({ msg: "Folder details removed.", folderInfo });
};

exports.onDeploymentFailure = async (user_id, folder_name) => {
  /**
   * This is used when deployment of multiple fails in between
   */

  try {
    await Folder.findOneAndUpdate(
      { user_id, folder_name },
      { deployment_failed: true }
    );
  } catch (error) {
    console.log(error);
  }
};

exports.isCanceledDeployment = async (req, res) => {
  /**
   * check if the user has canceled deployments
   */
  const fs = require("fs");

  const { id } = req.query;
  const path = `uploads/${id}`;
  let deleted = false;
  const data = await Folder.findOne({
    deployment_failed: true,
    user_id: id,
  });
  if (!data && fs.existsSync(path)) {
    fs.rmSync(path, { recursive: true });
    deleted = true;
  }
  return res.status(200).json({ deleted });
};

exports.returnAssets = async (user_id, folder_name) => {
  const data = await Folder.findOne(
    {
      user_id,
      folder_name,
    },
    { assets: 1, images: 1 }
  );
  return data;
};

exports.addTraits = async (req, res) => {
  const user_id = req.user._id;
  const { folder_name, nftId, metadata } = req.body;
  const json = { nftId, metadata };
  const exists = await Folder.countDocuments({ user_id, folder_name });
  if (!exists)
    return res
      .status(400)
      .json({ msg: "The folder you're trying to update does not exist." });

  const filter = { user_id, folder_name, "traits.nftId": { $ne: nftId } };
  const addNew = {
    $addToSet: { traits: { nftId, metadata } },
  };
  const options = {
    new: true,
    projection: { traits: { $elemMatch: { nftId: nftId } } },
  };
  const addTraits = await Folder.findOneAndUpdate(filter, addNew, options);
  const msg = addTraits
    ? `Traits added for item ${nftId}!`
    : `Traits already added for item ${nftId}, try updating!`;
  return res.status(200).json({ msg, data: addTraits });
};

exports.updateTraits = async (req, res) => {
  const { nftId } = req.params;
  const user_id = req.user._id;
  const { folder_name, metadata } = req.body;
  const exists = await Folder.countDocuments({ user_id, folder_name });
  if (!exists)
    return res
      .status(400)
      .json({ msg: "The folder you're trying to update does not exist." });

  const filter = { user_id, folder_name, "traits.nftId": nftId };
  const updateExisting = {
    $set: { "traits.$.metadata": metadata },
  };

  const options = {
    new: true,
    projection: { traits: { $elemMatch: { nftId: nftId } } },
  };
  const updateTraits = await Folder.findOneAndUpdate(
    filter,
    updateExisting,
    options
  );
  return res
    .status(200)
    .json({ msg: `Traits updated for item ${nftId}!`, data: updateTraits });
};
exports.getMetadata = async (req, res) => {
  const { folder_id } = req.query;
  const user_id = req.user._id;

  const exists = await Folder.countDocuments({ user_id, folder_id });
  if (!exists) return res.status(400).json({ msg: "Folder does not exist." });
  const metadataContent = await Metadata.find({
    folder_id,
  });

  res.status(200).json({ data: metadataContent });
};

exports.generateMetadataFromTraits = async (req, res) => {
  const { folder_name, collectionName, description } = req.body;
  const user_id = req.user._id;
  const exists = await Folder.countDocuments({ user_id, folder_name });

  const folderData = await Folder.aggregate([
    {
      $match: { user_id, folder_name },
    },
    {
      $project: {
        assets_size: { $size: "$assets.files" },
        baseURI: "$assets.baseURI",
        ext: "$assets.ext",
      },
    },
  ]);

  const metadataCount = await Metadata.countDocuments({
    folder_id: folderData[0]._id,
  });

  if (folderData[0].assets_size !== metadataCount) {
    return res
      .status(400)
      .json({ msg: "Assets and metadata count do not match!" });
  }
  const metadataContent = await Metadata.find({
    folder_id: folderData[0]._id,
  });
  const myDir = `uploads/${user_id}`;
  if (fs.existsSync(myDir)) fs.rmSync(myDir, { recursive: true, force: true });
  fs.mkdirSync(myDir);
  function createMetadata(document) {
    let jsonFile = {
      name: `${collectionName} #${document.nftId}`,
      image: `${folderData[0].baseURI}${document.nftId}${folderData[0].ext}`,
      description,
      attributes: document.attributes,
    };
    fs.writeFileSync(
      path.join(myDir, `${document.nftId}.json`),
      JSON.stringify(jsonFile),
      "utf-8"
    );
  }
  for (let metadata of metadataContent) {
    createMetadata(metadata);
  }
  const fileCount = fs.readdirSync(myDir).length;
  return res.status(200).json({ msg: `${fileCount} metadata files created!` });
};

exports.addMetadata = async (req, res) => {
  const user_id = req.user._id;
  const { folder_name, nftIdFrom, nftIdTo, attributes } = req.body;

  const folder = await Folder.findOne({ user_id, folder_name });
  if (!folder) return res.status(400).json({ msg: "Vault not found!" });

  if (Number(nftIdTo) > folder.assets.files.length)
    return res
      .status(400)
      .json({ msg: "Invalid range. Choose max range within asset size." });

  for (
    let currentNftId = Number(nftIdFrom);
    currentNftId <= Number(nftIdTo);
    currentNftId++
  ) {
    await Metadata.updateOne(
      {
        folder_id: folder._id,
        nftId: Number(currentNftId),
      },
      { attributes },
      { upsert: true }
    );
  }

  return res.status(200).json({ msg: "Attributes updated successfully" });
};
