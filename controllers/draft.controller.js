const { uploadToGoogleStore } = require("../helpers/googleStorage");
const Draft = require("../models/draft.model");
const { pagination } = require("../utils/pagination");

exports.createCollectionDraft = async (req, res) => {
  let imageUrl, media2Url;
  if (req.files.media2) {
    media2Url = await uploadToGoogleStore(req.files.media2[0]);
  } else if (req.body && req.body.media2) {
    media2Url = media2;
  }

  if (req.files.image) {
    imageUrl = await uploadToGoogleStore(req.files.image[0]);
  } else if (req.body && req.body.image) {
    imageUrl = image;
  }

  req.body.image = imageUrl;
  req.body.media2 = media2Url;
  let collectionDraft = new Draft({
    user_id: req.user._id,
    model_type: "collections",
    data: req.body,
  });
  await collectionDraft.save();
  res
    .status(200)
    .json({ success: true, msg: "Collections fetched.", collectionDraft });
};

exports.getCollectionDraft = async (req, res) => {
  const { id } = req.params;
  const draft = await Draft.findOne({ _id: id, model_type: "collections" });
  res.status(200).json({ success: true, msg: "Collections fetched.", draft });
};

exports.getCollectionDraftsOfUser = async (req, res) => {
  const { limit, page } = req.query;
  const { _id } = req.user;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const data = await Draft.find({ user_id: _id })
    .sort({ _id: -1 })
    .limit(getLimit)
    .skip(skip);
  const total = await Draft.countDocuments({ user_id: _id });
  res.status(200).json({
    success: true,
    msg: "Drafts fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};

exports.getApprovedDrafts = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = { "data.isApproved": true, model_type: "collections" };
  const data = await Draft.find(filter)
    .sort({ _id: -1 })
    .limit(getLimit)
    .skip(skip);
  const total = await Draft.countDocuments(filter);
  res.status(200).json({
    success: true,
    msg: "Drafts fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};
exports.editDraft = async (req, res) => {
  const { id } = req.params;
  let data = {};
  data = req.body;
  const { _id } = req.user;

  let imageUrl, media2Url;

  if (req.files.image) {
    imageUrl = await uploadToGoogleStore(req.files.image[0]);
  } else if (req.body && req.body.image) {
    imageUrl = req.body.image;
  }

  if (req.files.media2) {
    media2Url = await uploadToGoogleStore(req.files.media2[0]);
  } else if (req.body && req.body.media2) {
    media2Url = req.body.media2;
  }

  if (imageUrl) data.image = imageUrl;
  if (media2Url) data.media2 = media2Url;

  const updatedData = await Draft.findOneAndUpdate(
    { _id: id, user_id: _id },
    { $set: { data } },
    { new: true }
  );

  res.status(200).json({
    success: true,
    msg: "Draft edited.",
    data: updatedData,
  });
};
exports.changeDraftImage = async (req, res) => {
  const { id } = req.params;
  const { _id } = req.user;
  const image = await uploadToGoogleStore(req.file);
  const updatedData = await Draft.findOneAndUpdate(
    { _id: id, user_id: _id },
    { "data.image": image },
    { new: true }
  );
  res.status(200).json({
    success: true,
    msg: "Draft image updated.",
    data: updatedData,
  });
};
