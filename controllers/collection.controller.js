const CandyMachine = require("../models/candyMachine.model");
const Collection = require("../models/collection.model");
const WhitelistEntry = require("../models/whitelistEntries.model");
const Whitelist = require("../models/whitelist.model");
const axios = require("axios");

const {
  readCollections,
  countCollections,
  readCollection,
  deleteDraft,
  updateCollection,
} = require("../services/collection.service");
const {
  createUniqueUsername,
  txnhashExists,
} = require("../validations/collection.validation");
const { pagination } = require("../utils/pagination");
const { getMintData } = require("../helpers/aptos");
const { uploadToGoogleStore } = require("../helpers/googleStorage");

exports.createCollection = async (req, res) => {
  const {
    name,
    un,
    description,
    image,
    royalty_percentage,
    twitter,
    tweet,
    instagram,
    discord,
    website,
    resource_account,
    txnhash,
    candy_id,
    draft_id,
    isEdition,
    edition,
    seedz,
    coin_type,
    media2,
  } = req.body;
  let phases = JSON.parse(req.body.phases);
  if (await txnhashExists(txnhash))
    return res
      .status(400)
      .json({ msg: "Collection with this txnhash already exists." });
  if ((typeof image == "undefined" || image == "") && !req.files.image)
    return res
      .status(400)
      .json({ msg: "Please upload an image for collection." });
  const {
    presale_mint_price,
    presale_mint_time,
    public_sale_mint_price,
    public_sale_mint_time,
    royalty_payee_address,
    total_supply,
    baseuri,
  } = await getMintData(txnhash, candy_id);
  let denominator = Math.pow(10, 8);
  if (coin_type == "GUI") denominator = Math.pow(10, 6);
  const username = await createUniqueUsername(un, name);
  let imageUrl, media2Url;

  if (req.files.image) {
    imageUrl = await uploadToGoogleStore(req.files.image[0]);
  } else if (req.body && req.body.image) {
    imageUrl = image;
  } else {
    return res
      .status(400)
      .json({ msg: "Please upload an image for collection." });
  }

  if (req.files.media2) {
    media2Url = await uploadToGoogleStore(req.files.media2[0]);
  } else if (req.body && req.body.media2) {
    media2Url = media2;
  }

  const newCollection = new Collection({
    user_id: req.user._id,
    name,
    username,
    description,
    image: imageUrl,
    media2: media2Url,
    baseURL: baseuri,
    supply: total_supply,
    royalty_payee_address,
    royalty_percentage,
    twitter,
    tweet,
    instagram,
    discord,
    website,
    txnhash,
    isEdition,
    edition: isEdition ? edition : null,
    phases,
    "seed.seedz": seedz?.toLowerCase?.() === "true",
    "seed.coin_type": coin_type,
    "candyMachine.candy_id": candy_id,
    "candyMachine.resource_account": resource_account,
    "candyMachine.whitelist_sale_time": presale_mint_time * 1000,
    "candyMachine.public_sale_time": public_sale_mint_time * 1000,
    "candyMachine.public_sale_price": public_sale_mint_price / denominator,
    "candyMachine.whitelist_price": presale_mint_price / denominator,
  });
  await deleteDraft({ _id: draft_id });
  await newCollection.save();
  res.status(200).json({ msg: "Collection created!", newCollection });
};

exports.getCollections = async (req, res) => {
  // #swagger.tags = ['Collection']
  // #swagger.summary = 'Get all collections from database.'
  try {
    const { limit, page } = req.query;
    const { getLimit, skip } = pagination(page, limit);

    const data = await readCollections(
      getLimit,
      skip,
      { isEdition: false },
      {},
      { _id: -1 }
    );
    const total = await countCollections({ isEdition: false });
    res.status(200).json({
      success: true,
      msg: "Collections fetched.",
      page: Number(page),
      perPage: Number(limit),
      total,
      data,
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({ msg: "Some error occured." });
  }
};

exports.getEditions = async (req, res) => {
  const { limit, page, edition, user_id, isApproved } = req.query;
  const { getLimit, skip } = pagination(page, limit);
  let filter = { isEdition: true, edition };
  if (isApproved != undefined) filter.isApproved = isApproved;
  if (user_id) filter.user_id = user_id;

  const data = await readCollections(getLimit, skip, filter, {}, { _id: -1 });
  const total = await countCollections(filter);
  return res.status(200).json({
    success: true,
    msg: "Editions fetched.",
    page: Number(page),
    perPage: Number(limit),
    total,
    data,
  });
};

exports.getEdition = async (req, res) => {
  const { id } = req.params;
  const filter = { _id: id, isEdition: true };
  const editionData = await readCollection(filter);
  return res.status(200).json({ editionData });
};

exports.getFeaturedCollection = async (req, res) => {
  const collection = await readCollections(5, 0, {
    isApproved: true,
    isFeatured: true,
  });
  return res.status(200).json({
    success: true,
    msg: "Collection fetched.",
    collection,
  });
};
exports.getCollectionsOfUser = async (req, res) => {
  const { limit, page, user_id } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = { user_id, isEdition: false };
  const data = await readCollections(getLimit, skip, filter, {}, { _id: -1 });
  const total = await countCollections(filter);
  res.status(200).json({
    success: true,
    msg: "Collections fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};

exports.getUpcomingCollections = async (req, res) => {
  const { limit, page } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const query = {
    "candyMachine.whitelist_sale_time": {
      $gt: Date.now(),
    },
    isApproved: true,
    isEdition: false,
  };
  const data = await readCollections(
    getLimit,
    skip,
    query,
    {},
    { "candyMachine.whitelist_sale_time": 1 }
  );
  const total = await countCollections(query);
  res.status(200).json({
    success: true,
    msg: "Collections fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};

exports.getLiveCollections = async (req, res) => {
  const { limit, page, user_id } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = {
    "candyMachine.whitelist_sale_time": {
      $lt: Date.now(),
    },
    "status.sold_out": false,
    isApproved: true,
    isEdition: false,
  };

  user_id ? (filter.user_id = user_id) : null;
  const data = await readCollections(getLimit, skip, filter, {}, { _id: -1 });
  const total = await countCollections(filter);
  res.status(200).json({
    success: true,
    msg: "Collections fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};

exports.getUnderReviewCollections = async (req, res) => {
  const { limit, page, user_id } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = { isApproved: false, isEdition: false };
  user_id ? (filter.user_id = user_id) : null;
  const data = await readCollections(getLimit, skip, filter, {}, { _id: -1 });
  const total = await countCollections(filter);
  res.status(200).json({
    success: true,
    msg: "Collections fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};

exports.getApprovedCollections = async (req, res) => {
  const { limit, page, user_id } = req.query;
  const { getLimit, skip, getPage } = pagination(page, limit);
  const filter = { isApproved: true, isEdition: false };
  user_id ? (filter.user_id = user_id) : null;
  const data = await readCollections(getLimit, skip, filter, {}, { _id: -1 });
  const total = await countCollections(filter);
  res.status(200).json({
    success: true,
    msg: "Collections fetched.",
    page: getPage,
    perPage: getLimit,
    total,
    data,
  });
};

exports.getCollection = async (req, res) => {
  // #swagger.tags = ['Collection']
  // #swagger.summary = 'Get a single collection from database.'
  try {
    const { id } = req.params;
    const collection = await Collection.find(
      { _id: id },
      { royalty_payee_address: 0, royalty_percentage: 0 }
    );
    res.status(200).json({ collection });
  } catch (error) {
    res.status(500).json({ msg: "Some error occured." });
  }
};

exports.getCollectionFromUsername = async (req, res) => {
  // #swagger.tags = ['Collection']
  // #swagger.summary = 'Get a single collection from database.'
  try {
    const { username } = req.params;
    const collection = await Collection.find(
      { username: username },
      { royalty_payee_address: 0, royalty_percentage: 0 }
    );
    res.status(200).json({ collection });
  } catch (error) {
    res.status(500).json({ msg: "Some error occured." });
  }
};

exports.editCollection = async (req, res) => {
  const { id } = req.params;
  const { _id } = req.user;
  const updated = await updateCollection({ _id: id, user_id: _id }, req.body);
  res.status(200).json({
    success: true,
    msg: "Collection updated.",
    updatedCollection: updated,
  });
};

exports.deleteCollection = async (req, res) => {
  // #swagger.tags = ['Collection']
  // #swagger.summary = 'Delete a single collection from database.'
  try {
    const { id } = req.params;
    await Collection.findOneAndDelete({ _id: id }, { rawResult: true })
      .then(async function (data) {
        await CandyMachine.deleteOne({ _id: data.value.candyMachine_id });
        await WhitelistEntry.deleteMany({ collection_id: id });
        await Whitelist.deleteOne({ collection_id: id });
        res
          .status(200)
          .json({ msg: "Collection deleted.", deleted_collection: data.value });
      })
      .catch(function (error) {
        console.log(error);
      });
    return;
  } catch (error) {
    res.status(500).json({ msg: "Some error occured." });
  }
};
exports.deleteCollections = async (req, res) => {
  try {
    const collection = await Collection.deleteMany({});
    const candy = await CandyMachine.deleteMany({});
    res.status(200).json({ collection, candy });
  } catch (error) {
    res.status(500).json({ msg: "Some error occured." });
  }
};

exports.searchCollections = async (req, res) => {
  const { q, page, limit } = req.query;
  const { getLimit, skip } = pagination(page, limit);

  const result = await Collection.find({
    isApproved: true,
    name: { $regex: q, $options: "ix" },
  })
    .limit(getLimit)
    .skip(skip);
  res.status(200).json({ result });
};

exports.setSoldOut = async (req, res) => {
  // set status of a collection to sold out
  const { id } = req.body;
  const result = await Collection.findOneAndUpdate(
    { _id: id },
    { "status.sold_out": true, "status.time": Date.now() },
    { new: true }
  );
  res.status(200).json({ result: result.status });
};

exports.getPhases = async (req, res) => {
  const { collection_id } = req.query;
  const phases = await readCollection(
    {
      collection_id,
    },
    { phases: 1 }
  );
  res.status(200).json({ phases });
};

exports.getCollectionStats = async (req, res) => {
  let editionFilter = { isApproved: true, isEdition: true };
  let collectionFilter = { isApproved: true, isEdition: false };
  const totalEditions = await countCollections(editionFilter);
  const totalCollections = await countCollections(collectionFilter);
  const collections = await Collection.find({
    isApproved: true,
    minted: { $exists: true },
  });
  let totalMinted = 0;
  collections.forEach((collection) => {
    totalMinted += collection.minted;
  });
  return res.status(200).json({
    success: true,
    msg: "Stats fetched.",
    data: {
      totalMinted,
      totalCollections,
      totalEditions,
    },
  });
};

// exports.getFastestSoldout = async (req, res) => {
//   const data = await Collection.find({ "status.sold_out": true })
//     .sort({ "status.time": 1 })
//     .limit(1);
//   const result = await Collection.aggregate([
//     { $group: { _id: "$name", min: { $min: "$status.time" } } },
//   ]);
//   res.status(200).json({ data });
// };
exports.updateNftMintedCount = async () => {
  const collections = await Collection.find({ isApproved: true });
  for (let c of collections) {
    const resource_account = c.candyMachine.resource_account;
    const url = `https://fullnode.mainnet.aptoslabs.com/v1/accounts/${resource_account}/resources?limit=9999`;
    const resp = await axios.get(url);
    for (let r of resp.data) {
      if (r.type.includes("candymachine::CandyMachine")) {
        const dbMinted = Number(c.minted);
        const minted = Number(r.data.minted);
        if (!(dbMinted === minted)) {
          Collection.findOneAndUpdate(
            { _id: c._id },
            { $set: { minted: minted } },
            (err, doc) => {
              if (err) {
                console.log("Error updating documents!");
                return;
              }
            }
          );
        }
      }
    }
  }
};
