// const Bundlr = require("@bundlr-network/client");
const fs = require("fs");
const mime = require("mime-types");
const Folder = require("../models/folder.model");
const WhitelistEntry = require("../models/whitelistEntries.model");
const { dirSize, formatBytes, findCsv } = require("../utils/fileUtils");
const { returnAssets } = require("./folder.controller");
const privateKey = "./config/wallet.json";
const jwk = JSON.parse(fs.readFileSync(privateKey).toString());
const getIds = require("../utils/readArkb");
const path = require("path");
const {
  uploadFileWithTurbo,
  calculateUploadCost,
} = require("../uploader/index.js");

exports.singleFileUploadWithTurbo = async (req, res) => {
  try {
    let { name, description, attributes, newFolder } = req.body;
    attributes = JSON.parse(attributes);

    const filePath = `${newFolder}/` + req.file.filename;

    // Step 1: Upload the image file
    console.log("filePath from controller: ", filePath);
    const imageUpload = await uploadFileWithTurbo(filePath);
    console.log("imageUpload: ", imageUpload);
    const imageUrl = imageUpload.url;

    // Step 2: Create metadata JSON
    const metadata = {
      name,
      description,
      image: imageUrl,
      attributes,
    };

    const metadataFilePath = path.join(newFolder, "0.json");
    fs.writeFileSync(metadataFilePath, JSON.stringify(metadata));

    // Step 3: Upload the metadata file
    const metadataUpload = await uploadFileWithTurbo(metadataFilePath);
    const metadataUrl = metadataUpload.url;

    // Step 4: Respond
    res.status(200).json({
      success: true,
      msg: "File uploaded.",
      metadata: metadataUrl,
    });

    // Step 5: Clean up folder
    fs.rmSync(newFolder, { recursive: true, force: true });
  } catch (err) {
    console.error("Upload failed:", err);
    res.status(500).json({
      success: false,
      msg: "Upload failed.",
      error: err.message,
    });
  }
};

exports.createVideoEdition = async (req, res) => {
  try {
    let { name, description, attributes, newFolder } = req.body;

    // normalize/prepare metadata base
    const jsonfile = {
      name,
      description,
    };

    // grab uploaded files (e.g., from multer)
    const imageFile = req?.files?.image?.[0];
    const videoFile = req?.files?.video?.[0];

    if (!imageFile || !videoFile) {
      return res
        .status(400)
        .json({ error: "Image and video files are required." });
    }

    const imageRes = await uploadFileWithTurbo(imageFile.path);
    const videoRes = await uploadFileWithTurbo(videoFile.path);

    if (!imageRes?.url || !videoRes?.url) {
      return res
        .status(502)
        .json({ error: "Failed to upload media to Arweave." });
    }

    const imageLink = imageRes.url;
    const videoLink = videoRes.url;

    const fileProperties = {
      files: [
        {
          uri: imageLink,
          type: imageRes.type || imageFile.mimetype,
        },
        {
          uri: videoLink,
          type: videoRes.type || videoFile.mimetype,
        },
      ],
      category: "video",
    };

    jsonfile.animation_url = videoLink;
    jsonfile.image = imageLink;
    jsonfile.attributes =
      typeof attributes === "string"
        ? JSON.parse(attributes || "[]")
        : attributes || [];
    jsonfile.properties = fileProperties;

    // ensure folder exists, write metadata, then upload via Turbo
    if (!fs.existsSync(newFolder)) {
      fs.mkdirSync(newFolder, { recursive: true });
    }
    const metadataFilePath = path.join(newFolder, "0.json");
    fs.writeFileSync(
      metadataFilePath,
      JSON.stringify(jsonfile, null, 2),
      "utf-8"
    );

    const metadataRes = await uploadFileWithTurbo(metadataFilePath);
    if (!metadataRes?.url) {
      return res
        .status(502)
        .json({ error: "Failed to upload metadata to Arweave." });
    }
    const metadataLink = metadataRes.url;

    // cleanup local temp folder
    fs.rmSync(newFolder, { recursive: true, force: true });

    return res.status(200).json({
      data: { image: imageLink, video: videoLink, metadata: metadataLink },
    });
  } catch (err) {
    console.error("createVideoEdition error:", err);
    return res.status(500).json({ error: "Internal server error." });
  }
};
exports.folderUpload = async (req, res, next) => {
  const { folder_name, newFolder, user_id, type } = req.body;
  if (type == "assets" || type == "images") {
    res
      .status(200)
      .json({ msg: "Files uploaded to server", folder_name, newFolder });
  }
  if (type == "metadata") {
    const length = fs.readdirSync(newFolder).length;
    const assets = await returnAssets(user_id, folder_name);
    const assetExt = assets.assets.ext.toLowerCase();
    const validImgTypes = new Set([".png", ".jpg", ".jpeg", ".webp"]);
    const validVideoTypes = new Set([".mp4", ".mov"]);
    const validAudioTypes = new Set([".mp3"]);

    if (validImgTypes.has(assetExt)) {
      for (i = 0; i < length; i++) {
        const fileName = `${newFolder}/${i}.json`;
        let file = JSON.parse(
          fs.readFileSync(`${newFolder}/${i}.json`, "utf8")
        );

        file.image = `${assets.assets.baseURI}${i}${assets.assets.ext}`;
        fs.writeFile(fileName, JSON.stringify(file, null, 2), (err, data) => {
          if (err) {
            console.log(`Error at index ----> ${i}`, err);
          }
        });
      }
    }

    if (validVideoTypes.has(assetExt) || validAudioTypes.has(assetExt)) {
      const numberOfImages = assets.images.files.length;
      for (i = 0; i < length; i++) {
        const fileName = `${newFolder}/${i}.json`;
        let file = JSON.parse(
          fs.readFileSync(`${newFolder}/${i}.json`, "utf8")
        );
        let assetLink = `${assets.assets.baseURI}${i}${assets.assets.ext}`;
        let imageLink = `${assets.images.baseURI}${numberOfImages > 1 ? i : 0}${
          assets.images.ext
        }`;
        let fileProperties = {
          files: [
            {
              uri: imageLink,
              type: mime.lookup(imageLink),
            },

            {
              uri: assetLink,
              type: mime.lookup(assetLink),
            },
          ],
          category: "video",
        };
        file.animation_url = assetLink;
        file.image = imageLink;
        file.properties = fileProperties;
        fs.writeFile(fileName, JSON.stringify(file, null, 2), (err, data) => {
          if (err) {
            console.log(`Error at index ----> ${i}`, err);
          }
        });
      }
    }
    res
      .status(200)
      .json({ msg: "Files uploaded to server", folder_name, newFolder });
  }
  return;
};

exports.checkFund = async (req, res) => {
  const _id = req.user._id.toString();
  const folder = `uploads/${_id}`;

  if (!fs.existsSync(folder))
    return res
      .status(400)
      .json({ msg: "Please upload some files to check price." });

  const size = await dirSize(_id);
  const fileSize = formatBytes(size);
  const { winc } = await calculateUploadCost(size);
  res.status(200).json({ size: fileSize, price: winc });
};

exports.postDeploymentOperation = async (
  user_id,
  folder_name,
  folder,
  baseURL
) => {
  // const folder = `path/to/user/folder/on/server`;
  // const folder_name = `folder_name as saved on DB`
  const ids = getIds(folder);
  const uploaded = await Folder.findOneAndUpdate(
    { user_id, folder_name },
    { baseURL, $push: { files: { $each: ids } } },
    { new: true }
  );
  return { success: true, msg: `${ids.length} files uploaded`, uploaded };
};

exports.addToWhitelist = async (req, res) => {
  const { newFolder, collection_id, phase } = req.body;
  const csvFile = await findCsv(newFolder);
  const file = `${newFolder}/${csvFile}`;
  const { parse } = require("csv-parse");
  let wallets = [];
  function changeWallet(wallet) {
    const slicedWallet = wallet.slice(2, wallet.length);
    const paddedWallet = slicedWallet.padStart(64, "0");
    return "0x" + paddedWallet;
  }
  fs.createReadStream(file)
    .pipe(parse({ delimiter: ",", from_line: 1 }))
    .on("data", function (row) {
      if (row[0].length >= 35 && Number(row[1]) > 0) {
        let wallet = {
          phase,
          collection_id,
          wallet_address: row[0].length < 66 ? changeWallet(row[0]) : row[0],
          mint_limit: Number(row[1]),
        };
        wallets.push(wallet);
      }
    })
    .on("end", async function () {
      const updatePromises = wallets.map(async (item) => {
        try {
          await WhitelistEntry.updateOne(
            {
              collection_id: item.collection_id,
              phase: item.phase,
              wallet_address: item.wallet_address,
            },
            { mint_limit: item.mint_limit },
            { upsert: true }
          );
        } catch (error) {
          console.log(error, `on wallet:\n ${item.wallet_address}`);
        }
      });

      await Promise.all(updatePromises);
      fs.rmSync(file, { recursive: true }) && console.log("File removed.");
      res.status(200).json({
        success: true,
        msg: `${wallets.length} wallet addresses added to whitelist.`,
      });
    })
    .on("error", function (error) {
      console.log(error.message);
    });
  return;
};

// exports.createMetadata = async (req, res) => {
//   const { newFolder, name, description } = req.body;
//   const data = fs.readFileSync(req.file.path, "utf8");
//   const fulldata = data.split("\n");
//   const trait_types = fulldata[0].split(",");
//   for (let i = 0; i < fulldata.length - 2; i++) {
//     const row = fulldata[i + 1].split(",");
//     let jsonfile = { name: `${name} #${i}`, description, attributes: [] };
//     for (let j = 0; j < trait_types.length; j++) {
//       if (row[j]) {
//         json = { trait_type: trait_types[j].trim(), value: row[j].trim() };
//         jsonfile.attributes.push(json);
//       }
//     }
//     fs.writeFileSync(`${newFolder}/${i}.json`, JSON.stringify(jsonfile));
//   }
//   fs.rmSync(req.file.path);

//   res.status(200).json({
//     success: true,
//     msg: `${fulldata.length - 2} metadata files generated.`,
//   });
// };

exports.createMetadata = async (req, res) => {
  const csv = require("csv-parser");
  const user_id = req.user._id;
  const { newFolder, folder_name, name, description } = req.body;
  const assets = await returnAssets(user_id, folder_name);
  let counter = -1;
  fs.createReadStream(req.file.path)
    .pipe(csv())
    .on("data", (row) => {
      counter++;
      const fileName = `${newFolder}/${counter}.json`;
      const filteredObj = Object.entries(row)
        .map((x) => {
          if (x[1]) {
            return { trait_type: x[0], value: x[1] };
          }
        })
        .filter((v) => v != null);
      let jsonfile = {
        name: `${name} #${counter}`,
        description,
        image: `${assets.assets.baseURI}${counter}${assets.assets.ext}`,
        attributes: filteredObj,
      };

      fs.writeFile(fileName, JSON.stringify(jsonfile), (err) => {
        if (err) throw err;
      });
    })
    .on("end", () => {
      fs.rmSync(req.file.path);
      const files = fs.readdirSync(req.body.newFolder).length;
      const images = assets.assets.files.length;
      const error =
        files !== images ? "Image and metadata length do not match" : null;

      res.json({ files, images, error });
    });
};
