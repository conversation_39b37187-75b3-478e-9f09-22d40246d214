require("dotenv").config();
const { NODE_ENV } = process.env;
exports.uploadToGoogleStore = async (image) => {
  return new Promise((resolve, reject) => {
    const { Storage } = require("@google-cloud/storage");
    let projectId = "wapal-launchpad";
    let keyFilename = "./config/gc.json";
    let bucket_name = NODE_ENV == "prod" ? "wapal-image" : "wapalimage";
    const storage = new Storage({
      projectId,
      keyFilename,
    });
    const bucket = storage.bucket(bucket_name);
    const blob = bucket.file(Date.now() + "_" + image.originalname, {});
    const blobStream = blob.createWriteStream();
    blobStream.on("error", (error) => {
      reject(error.message);
    });

    blobStream.on("finish", () => {
      const publicUrl = `https://storage.googleapis.com/${bucket.name}/${blob.name}`;
      resolve(publicUrl);
      // await bucket.file(image.originalname).makePublic();
    });
    blobStream.end(image.buffer);
  });
};
