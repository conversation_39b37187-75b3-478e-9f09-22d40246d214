require("dotenv").config();
const { NETWORK, APTOS_API_KEY } = process.env;
NODE_URL = `https://aptos-${NETWORK}.nodereal.io/v1/${APTOS_API_KEY}/v1`;

const { AptosClient, AptosAccount, HexString } = require("aptos");
const client = new AptosClient(NODE_URL);

exports.getMintData = async (txnhash, candy_id) => {
  const data = await client.getTransactionByHash(txnhash);
  for (element of data.changes) {
    if (
      element.type == "write_resource" &&
      element.data.type == candy_id + "::candymachine::CandyMachine"
    ) {
      return element.data.data;
    }
  }
};
// nodeURL http://fullnode.mainnet.wapal.io/v1
exports.getNewAptosAccount = () => {
  const account = new AptosAccount();
  return account.address().hex();
};
