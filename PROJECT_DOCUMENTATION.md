# Wapal Backend API Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Components](#components)
4. [Data Model](#data-model)
5. [APIs](#apis)
6. [Blockchain Integration](#blockchain-integration)
7. [Scheduling & Jobs](#scheduling--jobs)
8. [Security](#security)
9. [Observability](#observability)
10. [Deployment](#deployment)
11. [Testing](#testing)
12. [Operations / Runbooks](#operations--runbooks)
13. [Glossary](#glossary)
14. [Changelog](#changelog)

## Overview

### Purpose

Wapal Backend is a Node.js API service that powers an NFT launchpad platform on the Aptos
blockchain. It provides comprehensive functionality for NFT collection management, whitelist
operations with Merkle proof generation, file uploads to Arweave, and auction systems.

### Goals

- Enable seamless NFT collection creation and management on Aptos
- Provide secure whitelist management with cryptographic proof generation
- Facilitate decentralized file storage via Arweave integration
- Support auction mechanisms for NFT trading
- Deliver real-time updates through WebSocket connections

### Non-Goals

- Direct blockchain transaction signing (delegated to client wallets)
- On-chain smart contract deployment
- Cross-chain interoperability beyond Aptos

### Tech Stack Summary

- **Runtime**: Node.js 20
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Cache**: Redis
- **File Storage**: Arweave via Turbo SDK
- **Blockchain**: Aptos via official SDK
- **Real-time**: Socket.IO
- **Authentication**: JWT
- **Documentation**: Swagger/OpenAPI
- **Containerization**: Docker

### High-Level Context Diagram

```mermaid
graph TD
    Client[Web Client] --> API[Wapal Backend API]
    API --> MongoDB[(MongoDB)]
    API --> Redis[(Redis Cache)]
    API --> Arweave[Arweave Network]
    API --> Aptos[Aptos Blockchain]
    API --> GCS[Google Cloud Storage]

    API --> Socket[Socket.IO Server]
    Socket --> Client

    Cron[Cron Jobs] --> API
    Workers[Worker Pool] --> API
```

## Architecture

### Logical Architecture

```mermaid
graph TD
    subgraph "Client Layer"
        WebApp[Web Application]
        Wallet[Aptos Wallet]
    end

    subgraph "API Layer"
        Express[Express Server]
        Socket[Socket.IO Server]
        Auth[JWT Auth Middleware]
        Cache[Cache Middleware]
        Upload[File Upload Middleware]
    end

    subgraph "Business Logic"
        CollectionCtrl[Collection Controller]
        WhitelistCtrl[Whitelist Controller]
        FolderCtrl[Folder Controller]
        AuctionCtrl[Auction Controller]
    end

    subgraph "Services"
        MerkleService[Merkle Proof Service]
        FileService[File Upload Service]
        AptosService[Aptos Integration]
    end

    subgraph "Data Layer"
        MongoDB[(MongoDB)]
        Redis[(Redis)]
        Arweave[Arweave Storage]
        GCS[Google Cloud Storage]
    end

    subgraph "External"
        AptosNet[Aptos Network]
    end

    WebApp --> Express
    Wallet --> AptosNet
    Express --> Auth
    Express --> Cache
    Express --> Upload
    Auth --> CollectionCtrl
    Cache --> Redis
    CollectionCtrl --> MerkleService
    FolderCtrl --> FileService
    FileService --> Arweave
    FileService --> GCS
    AptosService --> AptosNet
    CollectionCtrl --> MongoDB
    WhitelistCtrl --> MongoDB
```

### API Request Lifecycle

```mermaid
sequenceDiagram
    participant Client
    participant Express
    participant Auth
    participant Controller
    participant Service
    participant MongoDB
    participant Cache

    Client->>Express: HTTP Request
    Express->>Auth: Validate JWT Token
    Auth->>MongoDB: Verify User
    MongoDB-->>Auth: User Data
    Auth-->>Express: Authorized
    Express->>Controller: Route Handler
    Controller->>Cache: Check Cache
    Cache-->>Controller: Cache Miss
    Controller->>Service: Business Logic
    Service->>MongoDB: Database Query
    MongoDB-->>Service: Data
    Service-->>Controller: Processed Data
    Controller->>Cache: Store Result
    Controller-->>Express: Response
    Express-->>Client: HTTP Response
```

### Merkle Proof Generation Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant WorkerPool
    participant Worker
    participant Cache

    Client->>API: Request Proof
    API->>Cache: Check Cached Proof
    Cache-->>API: Cache Miss
    API->>WorkerPool: Submit Proof Job
    WorkerPool->>Worker: Assign to Worker Thread
    Worker->>Worker: Generate Merkle Tree
    Worker->>Worker: Calculate Proof
    Worker-->>WorkerPool: Proof Result
    WorkerPool-->>API: Proof Data
    API->>Cache: Store Proof
    API-->>Client: Return Proof
```

### Arweave File Upload Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Socket
    participant TurboSDK
    participant Arweave
    participant MongoDB

    Client->>API: Upload Files via Socket.IO
    API->>Socket: Emit Upload Event
    Socket->>TurboSDK: Execute turbo upload-folder
    TurboSDK->>Arweave: Upload to Arweave Network
    Arweave-->>TurboSDK: Transaction ID
    TurboSDK-->>Socket: Manifest JSON
    Socket->>API: Post-deployment Operation
    API->>MongoDB: Update Collection BaseURL
    API-->>Client: Upload Complete
```

### Cron Job Execution

```mermaid
sequenceDiagram
    participant Cron
    participant API
    participant Aptos
    participant MongoDB

    Note over Cron: Every 6 hours
    Cron->>API: updateNftMintedCount()
    API->>MongoDB: Get Approved Collections
    MongoDB-->>API: Collection List
    loop For each collection
        API->>Aptos: Query CandyMachine Resource
        Aptos-->>API: Minted Count
        API->>MongoDB: Update Collection.minted
    end

    Note over Cron: Every 10 days
    Cron->>API: deleteOldProofs()
    API->>MongoDB: Delete Proofs > 10 days old
```

## Components

### Core Services

#### Collection Service (`services/collection.service.js`)

**Purpose**: Manages NFT collection data operations
**Responsibilities**:

- CRUD operations for collections
- Collection filtering and pagination
- Draft management

**Public Interface**:

```javascript
readCollections(limit, skip, filter, project, sort);
readCollection(filter, project);
countCollections(filter);
deleteDraft(filter);
updateCollection(filter, params);
```

#### Whitelist Service (`services/whitelist.service.js`)

**Purpose**: Handles whitelist and proof management
**Responsibilities**:

- Whitelist entry management
- Merkle proof generation coordination
- Cache management for proofs

#### Folder Service (`services/folder.service.js`)

**Purpose**: Manages file upload metadata
**Responsibilities**:

- Folder creation and management
- File upload tracking
- Asset organization

### Controllers

#### Collection Controller (`controllers/collection.controller.js`)

**Purpose**: HTTP request handling for collections
**Key Endpoints**:

- `POST /api/collection/create` - Create new collection
- `GET /api/collection/all` - List all collections
- `GET /api/collection/live` - Get live collections
- `GET /api/collection/upcoming` - Get upcoming collections

**Configuration**:

```env
APTOS_API_KEY=your_aptos_api_key
NETWORK=mainnet|testnet
```

#### Whitelist Controller (`controllers/whitelist.controller.js`)

**Purpose**: Manages whitelist operations
**Key Features**:

- Whitelist creation and management
- Entry validation
- Integration with Discord/Twitter verification

#### Authentication Controller (`controllers/auth.controller.js`)

**Purpose**: User authentication and registration
**Features**:

- JWT token generation
- Wallet-based authentication
- User statistics

### Middleware Components

#### Authentication Middleware (`middlewares/auth.js`)

**Purpose**: JWT token validation
**Configuration**:

```env
JWT_KEY=your_jwt_secret_key
```

#### Cache Middleware (`middlewares/cache.js`)

**Purpose**: In-memory caching for API responses
**Features**:

- Configurable TTL
- Dependency-based cache invalidation
- Memory-based storage

#### File Upload Middleware (`middlewares/multer.js`)

**Purpose**: Handles multipart file uploads
**Limits**:

- File size: 100MB
- Field size: 8MB

### Worker Pool (`workers/worker_pool.js`)

**Purpose**: Manages CPU-intensive Merkle proof calculations
**Features**:

- Multi-threaded proof generation
- Worker thread management
- Load balancing across CPU cores

**Configuration**:

- Worker count: `os.cpus().length`
- Worker script: `workers/merkleproof_worker.js`

### Cache System (`cache/main.js`)

**Purpose**: Redis-based caching layer
**Features**:

- Automatic fallback when Redis unavailable
- JSON serialization/deserialization
- Configurable TTL

**Configuration**:

```env
REDIS_URL=redis://localhost:6379
```

## Data Model

### MongoDB Schema Overview

```mermaid
erDiagram
    Users ||--o{ Collections : creates
    Users ||--o{ Folders : owns
    Users ||--o{ WhitelistEntries : submits
    Collections ||--o{ Whitelists : has
    Collections ||--o{ Auctions : features
    Whitelists ||--o{ WhitelistEntries : contains
    Folders ||--o{ Metadata : stores
    WhitelistEntries ||--o{ Proofs : generates

    Users {
        ObjectId _id PK
        string wallet_address
        string email UK
        string website
        string twitter
        string discord
        boolean signed
        date joined_at
        array tokens
    }

    Collections {
        ObjectId _id PK
        ObjectId user_id FK
        string name
        string username UK
        string description
        string image
        string baseURL
        number supply
        string royalty_payee_address
        number royalty_percentage
        object candyMachine
        boolean isFeatured
        boolean isVerified
        boolean isApproved
        object phases
        number minted
        date created_at
    }

    Whitelists {
        ObjectId _id PK
        ObjectId user_id FK
        ObjectId collection_id FK
        string twitter
        string discord_server_name
        number whitelist_spots
        date whitelist_start
        date whitelist_end
    }

    WhitelistEntries {
        ObjectId _id PK
        ObjectId collection_id FK
        string wallet_address
        string twitter
        string discord
        number mint_limit
        string phase
        date created_at
    }

    Folders {
        ObjectId _id PK
        ObjectId user_id FK
        string folder_name
        object assets
        object metadata
        date created_at
    }

    Proofs {
        ObjectId _id PK
        ObjectId collection_id FK
        string wallet_address
        string phase
        array proof
        string root
        date updated_at
    }
```

### Key Indexes

- `Users.email` - Unique index for user lookup
- `Collections.username` - Unique index for collection URLs
- `WhitelistEntries.collection_id + wallet_address` - Compound index for whitelist queries
- `Proofs.collection_id + wallet_address + phase` - Compound index for proof lookups

## APIs

### REST Endpoints

#### Authentication APIs (`/api/auth`)

| Method | Endpoint    | Description         | Auth Required |
| ------ | ----------- | ------------------- | ------------- |
| POST   | `/register` | Register new user   | No            |
| POST   | `/login`    | Authenticate user   | No            |
| GET    | `/stats`    | Get user statistics | No            |

**Example Registration Request**:

```bash
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "wallet_address": "0x1234...",
    "email": "<EMAIL>",
    "twitter": "@username",
    "discord": "user#1234"
  }'
```

**Example Response**:

```json
{
  "success": true,
  "user": {
    "_id": "64a1b2c3d4e5f6789012345",
    "wallet_address": "0x1234...",
    "email": "<EMAIL>"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Collection APIs (`/api/collection`)

| Method | Endpoint    | Description              | Auth Required | Cache TTL |
| ------ | ----------- | ------------------------ | ------------- | --------- |
| POST   | `/create`   | Create collection        | Yes           | -         |
| GET    | `/all`      | List all collections     | No            | 30s       |
| GET    | `/live`     | Get live collections     | No            | 20s       |
| GET    | `/upcoming` | Get upcoming collections | No            | 30s       |
| GET    | `/approved` | Get approved collections | No            | 30s       |
| GET    | `/featured` | Get featured collections | No            | 30s       |
| GET    | `/user`     | Get user's collections   | No            | 30s       |
| GET    | `/:id`      | Get collection by ID     | No            | 30s       |
| PUT    | `/:id`      | Update collection        | Yes           | -         |
| DELETE | `/:id`      | Delete collection        | Yes           | -         |

**Example Collection Creation**:

```bash
curl -X POST http://localhost:8000/api/collection/create \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: multipart/form-data" \
  -F "name=My NFT Collection" \
  -F "description=A unique NFT collection" \
  -F "supply=1000" \
  -F "royalty_percentage=5" \
  -F "image=@collection-image.png" \
  -F "phases=[{\"id\":\"public\",\"mint_price\":0.1}]"
```

#### Whitelist APIs (`/api/whitelist`)

| Method | Endpoint      | Description                | Auth Required |
| ------ | ------------- | -------------------------- | ------------- |
| POST   | `/create`     | Create whitelist           | Yes           |
| POST   | `/entry`      | Add whitelist entry        | No            |
| GET    | `/proof`      | Get Merkle proof           | No            |
| POST   | `/root`       | Generate Merkle root       | No            |
| GET    | `/mint_limit` | Get mint limit for address | No            |
| DELETE | `/entry`      | Remove whitelist entry     | Yes           |

**Example Proof Request**:

```bash
curl "http://localhost:8000/api/whitelist/proof?collection_id=64a1b2c3d4e5f6789012345&wallet_address=0x1234...&phase=whitelist"
```

**Example Proof Response**:

```json
{
  "success": true,
  "proof": [
    "0x1234567890abcdef...",
    "0xabcdef1234567890...",
    "0x567890abcdef1234..."
  ],
  "mint_limit": 3,
  "root": "0xfedcba0987654321..."
}
```

### WebSocket Events

#### Socket.IO Namespace: Default (`/`)

**Client Events**:

- `upload` - Trigger file upload to Arweave
  - Parameters: `user_id`, `folder_name`, `folder_path`, `type`

**Server Events**:

- `output` - Real-time upload progress
- `post-deployment` - Upload completion notification

**Example WebSocket Usage**:

```javascript
const socket = io("http://localhost:8000");

socket.emit("upload", userId, folderName, folderPath, "collection");

socket.on("output", (data) => {
  console.log("Upload progress:", data);
});

socket.on("post-deployment", (response) => {
  console.log("Upload complete:", response);
});
```

### Error Handling

**Standard Error Response Format**:

```json
{
  "success": false,
  "status": 400,
  "message": "Validation error message",
  "stack": {} // Only in development
}
```

**Common HTTP Status Codes**:

- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `413` - Payload Too Large (file size exceeded)
- `500` - Internal Server Error

### Rate Limiting

- TODO: Implement rate limiting middleware
- Recommended: 100 requests per minute per IP

## Blockchain Integration

### Aptos Integration

#### Connection Configuration

```javascript
// helpers/aptos.js
const NODE_URL = `https://aptos-${NETWORK}.nodereal.io/v1/${APTOS_API_KEY}/v1`;
const client = new AptosClient(NODE_URL);
```

**Environment Variables**:

```env
NETWORK=mainnet  # or testnet
APTOS_API_KEY=aptos_api_key
```

#### CandyMachine Integration

**Purpose**: Manages NFT minting on Aptos blockchain

**Key Functions**:

- `getMintData(txnhash, candy_id)` - Extract mint data from transaction
- `getNewAptosAccount()` - Generate new Aptos account address

**CandyMachine Resource Structure**:

```move
struct CandyMachine has key {
    collection_name: String,
    description: String,
    uri: String,
    minted: u64,
    supply: u64,
    // ... other fields
}
```

#### Transaction Monitoring

**Mint Count Updates**:

```javascript
// Scheduled every 6 hours
async function updateNftMintedCount() {
  const collections = await Collection.find({ isApproved: true });

  for (const collection of collections) {
    try {
      const resource = await client.getAccountResource(
        collection.candyMachine.resource_account,
        `${collection.candyMachine.candy_id}::candymachine::CandyMachine`
      );

      await Collection.findOneAndUpdate(
        { _id: collection._id },
        { minted: resource.data.minted }
      );
    } catch (error) {
      console.error(
        `Failed to update minted count for ${collection.name}:`,
        error
      );
    }
  }
}
```

#### Risk Controls

**Transaction Confirmation**:

- Wait for transaction finalization before updating database
- Retry mechanism for failed API calls
- Timeout handling for slow responses

**Settlement Safety**:

- Validate transaction signatures
- Cross-reference on-chain data with database state
- Alert on discrepancies

### Key Management

**Wallet Configuration**:

- Arweave wallet: `config/wallet.json` (JWK format)
- Google Cloud service account: `config/gc.json`
- Private keys stored outside repository (`.gitignore`)

**Security Practices**:

- No private key exposure in logs
- Encrypted storage in production
- Regular key rotation procedures

## Scheduling & Jobs

### Cron Job Configuration

The application uses `node-cron` for scheduled tasks defined in `app.js`:

```javascript
// Update NFT minted counts every 6 hours
cron.schedule("0 */6 * * *", () => {
  updateNftMintedCount();
  console.log("Mint in Collection Updated");
});
```

### Job Descriptions

#### `updateNftMintedCount`

**Purpose**: Synchronize on-chain minted counts with database
**Frequency**: Every 6 hours
**Process**:

1. Query all approved collections from MongoDB
2. For each collection, fetch CandyMachine resource from Aptos
3. Update `Collection.minted` field with on-chain data
4. Log errors for failed updates

**Failure Handling**:

- Individual collection failures don't stop the batch
- Errors logged but don't crash the application
- Retry logic: Manual restart required

### Idempotency

- Proof generation: Cached results prevent duplicate calculations
- Collection updates: Last-write-wins strategy
- File uploads: Unique folder names prevent conflicts

### Monitoring

- Cron job execution logged via Winston
- TODO: Add metrics for job success/failure rates
- TODO: Implement alerting for job failures

## Security

### Threat Model

#### Trust Boundaries

1. **Client ↔ API**: Untrusted clients, validate all inputs
2. **API ↔ Database**: Trusted internal network
3. **API ↔ Blockchain**: Semi-trusted, verify responses
4. **API ↔ File Storage**: Trusted, authenticated connections

#### Attack Vectors

- **Input Validation**: NoSQL injection, XSS
- **Authentication**: JWT token theft, replay attacks
- **File Upload**: Malicious file uploads, path traversal
- **Rate Limiting**: DDoS, resource exhaustion
- **Blockchain**: Transaction manipulation, oracle attacks

### Authentication & Authorization

#### JWT Implementation

```javascript
// Token generation
const token = jwt.sign({ id: user._id }, process.env.JWT_KEY, {
  expiresIn: "24h",
});

// Token validation middleware
const auth = async (req, res, next) => {
  const token = req.headers["authorization"]?.split(" ")[1];
  const data = jwt.verify(token, process.env.JWT_KEY);
  const user = await User.findOne({ _id: data.id, "tokens.token": token });
  req.user = user;
  next();
};
```

#### Authorization Levels

- **Public**: Collection browsing, proof generation
- **Authenticated**: Collection creation, whitelist management
- **Admin**: Collection approval, user management (TODO)

### Input Validation

#### File Upload Security

```javascript
// Multer configuration
const upload = multer({
  storage: multer.diskStorage({...}),
  limits: {
    fileSize: 100 * 1024 * 1024,  // 100MB
    fieldSize: 8 * 1024 * 1024    // 8MB
  },
});
```

#### Data Sanitization

- Mongoose schema validation
- Wallet address format validation
- File type restrictions (images, JSON)

### Secrets Management

#### Environment Variables

```env
# Database
MONGO_URL=mongodb://localhost:27017/wapal
REDIS_URL=redis://localhost:6379

# Authentication
JWT_KEY=your_jwt_secret_key

# Blockchain
APTOS_API_KEY=your_aptos_api_key
NETWORK=mainnet

# CORS
ORIGIN1=https://app.wapal.io
ORIGIN2=https://staging.wapal.io
ORIGIN3=http://localhost:3000

# File Storage
BUNDLR_NODE=https://node1.bundlr.network
```

#### Key Storage

- Production: Environment variables or secret management service
- Development: `.env` file (gitignored)
- Staging: Separate environment with test keys

### Encryption

#### Data at Rest

- MongoDB: Encryption handled by database layer
- Redis: No sensitive data cached
- File uploads: Public on Arweave (by design)

#### Data in Transit

- HTTPS enforced in production
- MongoDB connection over TLS
- Redis connection over TLS

### Dependency Security

- Regular `npm audit` checks
- Automated dependency updates via Dependabot
- Pin major versions to prevent breaking changes

## Observability

### Logging

#### Winston Configuration

```javascript
// middlewares/logger.js
const logger = createLogger({
  level: "info",
  format: format.combine(
    format.timestamp(),
    format.errors({ stack: true }),
    format.json()
  ),
  transports: [
    new transports.File({ filename: "logs/error.log", level: "error" }),
    new transports.File({ filename: "logs/combined.log" }),
    new transports.Console(),
  ],
});
```

#### Log Levels

- **Error**: Application errors, failed API calls
- **Warn**: Deprecated features, performance issues
- **Info**: Request logs, cron job execution
- **Debug**: Detailed execution flow (development only)

#### Structured Logging

```javascript
logger.info("Collection created", {
  userId: req.user._id,
  collectionId: collection._id,
  collectionName: collection.name,
  timestamp: new Date().toISOString(),
});
```

### Metrics

#### Business Metrics 'GET /stats'

- Collections created per day
- Whitelist entries added per day
- File uploads completed per day
- Proof generation requests per day

#### Infrastructure Metrics (TODO)

- CPU usage
- Memory usage
- Database connection pool size
- Redis cache hit rate

### Health Checks

#### Endpoint: `GET /health` (TODO)

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "services": {
    "mongodb": "healthy",
    "redis": "healthy",
    "aptos": "healthy"
  },
  "version": "1.0.0"
}
```

## Deployment

### Local Development Setup

#### Prerequisites

- Node.js 20+
- MongoDB 6.0+
- Redis 7.0+
- Git

#### Installation Steps

```bash
# Clone repository
git clone https://github.com/wapal/wapal-backend.git
cd wapal-backend

# Install dependencies
npm install

# Install global dependencies
npm install -g @ardrive/turbo-sdk

# Setup environment
cp .env.example .env
# Edit .env with your configuration

# Setup configuration files
# Add wallet.json and gc.json to config/ directory

# Start services
docker-compose up -d mongodb redis  # or start locally

# Start application
npm run dev
```

#### Environment Configuration

```env
# .env.example
PORT=8000
NODE_ENV=development
MONGO_URL=mongodb://localhost:27017/wapal_dev
REDIS_URL=redis://localhost:6379
JWT_KEY=your_development_jwt_key
APTOS_API_KEY=your_aptos_testnet_key
NETWORK=testnet
ORIGIN1=http://localhost:3000
ORIGIN2=http://localhost:3001
ORIGIN3=http://localhost:8080
```

### CI/CD Pipeline

#### GitHub Actions Workflow

**Production Deployment** (`.github/deploy-production.yml`):

```yaml
name: Deploy to Production
on:
  push:
    branches: [master]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.PROD_HOST }}
          username: ${{ secrets.PROD_USER }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /path/to/wapal-backend
            git pull origin master
            docker-compose down
            docker-compose up -d --build
```

**Staging Deployment** (`.github/deploy-staging.yml`):

```yaml
name: Deploy to Staging
on:
  push:
    branches: [staging]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to staging
        # Similar to production but with staging environment
```

### Docker Configuration

#### Dockerfile

```dockerfile
FROM node:20

WORKDIR /
COPY ./ ./
RUN npm i -g @ardrive/turbo-sdk
RUN npm install --loglevel=error

EXPOSE 8000
CMD ["node", "app.js"]
```

#### Docker Compose (Production)

```yaml
version: "3.1"
services:
  wapal-production-backend:
    container_name: ${CONTAINER_NAME}
    hostname: backend-server
    build:
      context: ./
      dockerfile: Dockerfile
    restart: always
    user: root:root
    ports:
      - ${PORT}:${PORT}
    environment:
      - NODE_ENV=production
    volumes:
      - ./config:/config:ro
      - ./logs:/logs
```

## Testing

### Test Structure

#### Current State

- **Unit Tests**: Not implemented
- **Integration Tests**: Not implemented
- **E2E Tests**: Not implemented
- **Load Tests**: Not implemented

#### Recommended Test Structure

```
tests/
├── unit/
│   ├── controllers/
│   ├── services/
│   ├── models/
│   └── utils/
├── integration/
│   ├── api/
│   ├── database/
│   └── blockchain/
├── e2e/
│   ├── user-flows/
│   └── admin-flows/
└── load/
    ├── api-load.js
    └── websocket-load.js
```

## Operations / Runbooks

### Common Issues & Solutions

#### High Memory Usage

**Symptoms**: Application becomes slow, potential OOM errors
**Investigation**:

```bash
# Check memory usage
docker stats wapal-backend
# Check for memory leaks
node --inspect app.js
```

**Resolution**:

- Restart application container
- Review recent code changes for memory leaks
- Scale horizontally if needed

#### Database Connection Issues

**Symptoms**: "MongoNetworkError" in logs
**Investigation**:

```bash
# Check MongoDB status
docker logs mongodb-container
# Test connection
mongo mongodb://localhost:27017/wapal
```

**Resolution**:

- Restart MongoDB container
- Check network connectivity
- Verify connection string and credentials

#### Redis Cache Issues

**Symptoms**: Slow API responses, cache misses
**Investigation**:

```bash
# Check Redis status
redis-cli ping
# Monitor cache hit rate
redis-cli info stats
```

**Resolution**:

- Restart Redis container
- Clear cache if corrupted: `redis-cli flushall`
- Check Redis memory limits

#### Arweave Upload Failures

**Symptoms**: File upload timeouts, WebSocket errors
**Investigation**:

```bash
# Check Turbo SDK balance
node topup.js
# Test Arweave connectivity
curl https://arweave.net/info
```

**Resolution**:

- Top up Arweave wallet balance
- Retry failed uploads
- Check network connectivity to Arweave

### Monitoring Runbooks

#### High Error Rate Alert

1. Check application logs for error patterns
2. Verify external service availability (MongoDB, Redis, Aptos)
3. Check recent deployments for correlation
4. Scale resources if needed
5. Rollback if deployment-related

#### Slow Response Time Alert

1. Check database query performance
2. Monitor cache hit rates
3. Review recent code changes
4. Check external API response times
5. Consider horizontal scaling

## Glossary

- **Aptos**: Layer 1 blockchain platform for smart contracts
- **Arweave**: Permanent, decentralized storage network
- **CandyMachine**: Smart contract for NFT minting on Aptos
- **JWT**: JSON Web Token for authentication
- **Merkle Proof**: Cryptographic proof of inclusion in a Merkle tree
- **NFT**: Non-Fungible Token, unique digital asset
- **PDA**: Program Derived Address (Aptos concept)
- **Turbo SDK**: Arweave's upload service SDK
- **Whitelist**: List of approved addresses for early/discounted minting

## Changelog

### Major Milestones

#### v1.0.0 (2023-01-17) - Initial Release

- Basic Express.js API with MongoDB
- User registration and authentication
- NFT collection management
- File upload to Bundlr/Arweave

#### v1.1.0 (2023-02-05) - Documentation & API Improvements

- Swagger API documentation
- Collection API refactoring
- Improved pagination and filtering

#### v1.2.0 (2023-03-03) - Merkle Proof System

- Merkle tree generation for whitelists
- Proof verification endpoints
- Worker pool for CPU-intensive operations

#### v1.3.0 (2023-04-27) - Scheduled Jobs

- Cron jobs for automated tasks
- NFT mint count synchronization
- Wallet address management

#### v1.4.0 (2023-05-16) - Observability

- Winston logging integration
- Structured error handling
- Performance monitoring

#### v1.5.0 (2023-12-14) - Feature Enhancements

- Edition collections support
- Enhanced filtering options
- Cache optimization

#### v1.6.0 (2024-04-21) - Blockchain Integration

- Real-time mint count updates
- Aptos blockchain monitoring
- Transaction verification

#### v1.7.0 (2024-07-10) - Maintenance & Cleanup

- Automated proof cleanup
- Database optimization
- Security improvements

### Recent Changes (Last 6 Months)

- Migrated from Bundlr to Turbo SDK for Arweave uploads
- Improved cache TTL configuration
- Enhanced error handling and logging
- Database index optimization

---

**Document Version**: 1.0
**Last Updated**: 2024-01-15
**Next Review**: 2024-04-15
