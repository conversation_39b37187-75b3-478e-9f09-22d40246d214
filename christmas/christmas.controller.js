const ChristmasTokenData = require("./christmasTokens.model");
const { generateRandomString } = require("../utils/stringOperations");

function numberOfTokens(num, len) {
  let tokens = [];
  for (let i = 0; i < num; i++) {
    const token = generateRandomString(len);
    tokens.push(token);
  }
  return tokens;
}
exports.generateToken = async (req, res) => {
  let { wallet_address, token } = req.body;

  const tokenUsed = await ChristmasTokenData.countDocuments({
    enteredWith: token,
  });

  const existsToken = await ChristmasTokenData.findOne({
    generated: { $in: [token] },
  });
  if (!existsToken) {
    return res.status(400).json({ msg: `Invalid token.` });
  }
  if (tokenUsed) return res.status(400).json({ msg: `Token already used.` });
  if (existsToken) {
    await ChristmasTokenData.findOneAndUpdate(
      { _id: existsToken._id },
      { $push: { used_by: wallet_address } }
    );
  }

  const tokens = numberOfTokens(5, 5);

  const newChristmasTokenData = new ChristmasTokenData({
    wallet_address,
    enteredWith: token,
    generated: tokens,
  });
  // newChristmasTokenData.used_by.push(wallet_address);
  await newChristmasTokenData.save();
  return res.status(200).json({
    success: true,
    msg: "Token data updated.",
    newData: newChristmasTokenData,
  });
};

exports.getInviteCodesOfUser = async (req, res) => {
  const { wallet_address } = req.query;
  const filter = { wallet_address };
  const exists = await ChristmasTokenData.countDocuments(filter);
  if (!exists)
    return res.status(400).json({ msg: `No tokens from this user.` });
  const resp = await ChristmasTokenData.findOne(filter);
  return res.status(200).json({
    success: true,
    msg: "Tokens generated by user fetched.",
    data: resp,
  });
};

exports.checkIfUsed = async (req, res) => {
  const { token } = req.query;
  const used = await ChristmasTokenData.countDocuments({ enteredWith: token });
  return res.status(200).json({
    success: true,
    msg: "Token status fetched.",
    data: Boolean(used),
  });
};
