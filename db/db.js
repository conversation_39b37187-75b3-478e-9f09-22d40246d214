const mongoose = require("mongoose");
const Proof = require("../models/proof.model");

const MONGO_URL = process.env.MONGO_URL;

const connectionParams = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  autoIndex: false, // safer in production
};

async function initDatabase() {
  try {
    await mongoose.connect(MONGO_URL, connectionParams);
    console.log("✅ Connected to MongoDB");

    await Proof.syncIndexes();
    console.log("✅ Indexes synced for Proof model");
  } catch (error) {
    console.error("❌ Failed to connect or sync indexes:", error);
    process.exit(1);
  }
}

module.exports = initDatabase;
