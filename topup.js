const fs = require("fs");
const path = require("path");
const {
  TurboFactory,
  ArweaveSigner,
  WinstonToTokenAmount,
} = require("@ardrive/turbo-sdk");

// Function to convert AR to winston and top up
async function topUpWithAR(arAmount) {
  // Convert AR to winston
  const winstonAmount = arAmount * 10 ** 12;

  // Load the JWK file for authentication
  const jwk = JSON.parse(
    fs.readFileSync(path.join(__dirname, "config/wallet.json"), "utf-8")
  );

  // Create a signer instance
  const signer = new ArweaveSigner(jwk);

  // Initialize the Turbo client with the Arweave token
  const turbo = TurboFactory.authenticated({ signer, token: "arweave" });

  try {
    // Define the top-up amount
    const tokenAmount = WinstonToTokenAmount(winstonAmount);

    // Perform the top-up with the specified token amount and fee multiplier
    const { winc, status, id, ...fundResult } = await turbo.topUpWithTokens({
      tokenAmount: tokenAmount,
      //   feeMultiplier: 1.1, // 10% fee increase for improved mining chances
    });

    // Display the result
    console.log("Top-up Successful!");
    console.log(`Status: ${status}`);
    console.log(`Transaction ID: ${id}`);
    console.log(`Funds Added: ${winc} WINC`);
    console.log("Additional Result:", fundResult);
  } catch (error) {
    console.error("Error during top-up process:", error);
  }
}

// Function to check the current balance
async function checkBalance() {
  // Load the JWK file for authentication
  const jwk = JSON.parse(
    fs.readFileSync(path.join(__dirname, "config/wallet.json"), "utf-8")
  );

  // Create a signer instance
  const signer = new ArweaveSigner(jwk);

  // Initialize the Turbo client with the Arweave token
  const turbo = TurboFactory.authenticated({ signer, token: "arweave" });

  try {
    // Fetch the balance
    const result = await turbo.getBalance();
    console.log(result);
    console.log(`Current Balance: ${result.winc} WINC`);
    console.log("Balance: ", result.balance * 10 ** -12);
  } catch (error) {
    console.error("Error checking balance:", error);
  }
}

// topUpWithAR(5);

checkBalance();
