const fs = require("fs");
const path = require("path");
const mime = require("mime-types");
const { TurboFactory } = require("@ardrive/turbo-sdk");

// import mime from "mime-types";
// import fs from "fs";
// import path from "path";
// import { TurboFactory } from "@ardrive/turbo-sdk";

async function getTurboClient() {
  const jwk = JSON.parse(fs.readFileSync("./config/wallet.json", "utf8"));
  return await TurboFactory.authenticated({ privateKey: jwk });
}

async function uploadFolder(folderPath) {
  const turbo = await getTurboClient();

  const result = await turbo.uploadFolder({
    folderPath,
    // indexFile: "index.html", // optional
  });

  const manifestId = result.manifestResponse?.id;
  const fileIds = result.fileResponses?.map((f) => f.id) || [];
  const manifestPaths = Object.keys(result.manifest?.paths || {});
  const indexPath = result.manifest?.index?.path;

  return {
    url: manifestId ? `https://arweave.net/${manifestId}` : null,
    manifestId,
    type: "folder",
    indexFile: indexPath,
    fileCount: fileIds.length,
    fileIds,
    fileNames: manifestPaths,
  };
}

async function uploadFile(filePath) {
  const turbo = await getTurboClient();
  console.log(turbo.uploadService.httpService.axios.getUri());
  // const filePath = path.join(__dirname, file_at);
  const mimeType = mime.lookup(filePath) || "application/octet-stream";
  const fileSize = fs.statSync(filePath).size;
  const { id } = await turbo.uploadFile({
    fileStreamFactory: () => fs.createReadStream(filePath),
    fileSizeFactory: () => fileSize,
    dataItemOpts: {
      tags: [
        {
          name: "Content-Type",
          value: mimeType,
        },
      ],
    },
  });

  return {
    url: `https://arweave.net/${id}/`,
    type: mimeType,
    id,
    name: path.basename(filePath),
  };
}
const calculateUploadCost = async (file_at) => {
  const turbo = await getTurboClient();

  const [uploadCostForFile] = await turbo.getUploadCosts({ bytes: [1024] });
  const { winc, adjustments } = uploadCostForFile;
  console.log({ winc, adjustments });
  return { winc, adjustments };
};

async function main() {
  //   try {
  //     // const folderResult = await uploadFolder("./uploads/test_1");
  //     // console.log("Folder Upload Result:", folderResult);

  //     //   file upload
  // const fileResult = await uploadFile("./uploads/images/0.png");
  const fileResult = await calculateUploadCost("./uploads/images/0.png");
  console.log("File Upload Result:", fileResult);
  //   } catch (err) {
  //     console.error("Upload failed:", err);
  //   }
}

main();
