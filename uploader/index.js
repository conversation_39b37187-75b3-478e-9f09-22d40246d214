const {
  TurboFactory,
  defaultTurboConfiguration,
  developmentUploadServiceURL,
  defaultUploadServiceURL,
} = require("@ardrive/turbo-sdk");
const fs = require("fs");
const path = require("path");
const mime = require("mime-types");

async function getTurboClient() {
  const jwk = JSON.parse(fs.readFileSync("./config/wallet.json", "utf8"));
  return await TurboFactory.authenticated({
    privateKey: jwk,
  });
}
const uploadFolderWithTurbo = async (folderPath) => {
  const turbo = await getTurboClient();

  const result = await turbo.uploadFolder({
    folderPath,
    // indexFile: "index.html", // optional
  });

  const manifestId = result.manifestResponse?.id;
  const fileIds = result.fileResponses?.map((f) => f.id) || [];
  // const manifestPaths = Object.keys(result.manifest?.paths || {});
  const indexPath = result.manifest?.index?.path;

  return {
    url: manifestId ? `https://arweave.net/${manifestId}` : null,
    manifestId,
    type: "folder",
    indexFile: indexPath,
    fileCount: fileIds.length,
    fileIds,
  };
};

const uploadFileWithTurbo = async (file_at) => {
  try {
    const turbo = await getTurboClient();

    const filePath = path.join(__dirname, `../${file_at}`);

    const mimeType = mime.lookup(filePath) || "application/octet-stream";

    const fileSize = fs.statSync(filePath).size;
    const result = await turbo.uploadFile({
      fileStreamFactory: () => fs.createReadStream(filePath),
      fileSizeFactory: () => fileSize,
      dataItemOpts: {
        tags: [
          {
            name: "Content-Type",
            value: mimeType,
          },
        ],
      },
    });
    const { id } = result;

    return {
      url: `https://arweave.net/${id}/`,
      type: mimeType,
      id,
      name: path.basename(filePath),
    };
  } catch (error) {
    console.log(error);
  }
};

const calculateUploadCost = async (bytes) => {
  const turbo = await getTurboClient();

  const [uploadCostForFile] = await turbo.getUploadCosts({
    bytes: [bytes || 1024],
  });
  const { winc, adjustments } = uploadCostForFile;
  console.log({ winc, adjustments });
  return { winc, adjustments };
};

module.exports = {
  uploadFileWithTurbo,
  uploadFolderWithTurbo,
  calculateUploadCost,
};
