const swaggerAutogen = require("swagger-autogen")();
require("dotenv").config();
const host = (process.env.MODE = "Dev");
const schemes =
  process.env.MODE === "Development"
    ? ["http"]
    : ["https", "http"]
    ? "localhost:3000"
    : "staging-api.wapal.io";
const doc = {
  info: {
    title: "Wapal API",
    description: "Wapal API documentation",
  },
  host: host,
  tags: [
    {
      name: "Collection",
      description: "Collection related APIs.",
    },
    {
      name: "Folder",
      description: "Folder related APIs.",
    },
  ],
  schemes: schemes,
};

const outputFile = "./swag/swagger-output.json";
const endpointsFiles = ["./routes/index"];
swaggerAutogen(outputFile, endpointsFiles, doc);
