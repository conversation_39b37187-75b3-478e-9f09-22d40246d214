const router = require("express").Router();
const {
  getAllWhitelists,
  createWhitelist,
  getAllWhitelistsOfUser,
  getWhitelist,
  createWhitelistEntry,
  getWhitelistEntries,
  getWhitelistFromCollectionUsername,
  searchWhitelistEntries,
} = require("../controllers/whitelist.controller");
const {
  proof,
  root,
  getMintLimit,
  deleteWhitelistEntry,
  clearCache,
} = require("../controllers/whitelist");

const auth = require("../middlewares/auth");
const use = require("../utils/HOF_promise");

router.post("/create", auth, use(createWhitelist));
router.get("/all", use(getAllWhitelists));
router.get("/user", use(getAllWhitelistsOfUser));
router.post("/root", use(root));
router.get("/proof", use(proof));
router.post("/entry", use(createWhitelistEntry));
router.get("/entry", use(getWhitelistEntries));
router.get("/find", use(searchWhitelistEntries));
router.get("/mint_limit", use(getMintLimit));
router.delete("/entry", auth, use(deleteWhitelistEntry));
router.get("/clear", use(clearCache));
router.get("/:un", use(getWhitelistFromCollectionUsername));
router.get("/:id", use(getWhitelist));

module.exports = router;
