const router = require("express").Router();
const cache = require("../middlewares/cache");
const use = require("../utils/HOF_promise");
const {
  getCollections,
  getCollection,
  createCollection,
  deleteCollection,
  deleteCollections,
  getCollectionsOfUser,
  searchCollections,
  setSoldOut,
  getCollectionFromUsername,
  getFeaturedCollection,
  getLiveCollections,
  getUnderReviewCollections,
  getUpcomingCollections,
  getApprovedCollections,
  getPhases,
  editCollection,
  getEditions,
  getEdition,
  getCollectionStats,
} = require("../controllers/collection.controller");
const auth = require("../middlewares/auth");
const upload = require("../middlewares/multerMemstore");

router.post(
  "/create",
  auth,
  upload.fields([
    { name: "image", maxCount: 1 },
    { name: "media2", maxCount: 1 },
  ]),
  use(createCollection)
);

router.get("/all", cache({ timeOut: 30 * 1000 }), getCollections);
router.get("/user", cache({ timeOut: 30 * 1000 }), use(getCollectionsOfUser));
router.get(
  "/approved",
  cache({ timeOut: 30 * 1000 }),
  use(getApprovedCollections)
);
router.get("/live", cache({ timeOut: 20 * 1000 }), use(getLiveCollections));
router.get(
  "/upcoming",
  cache({ timeOut: 30 * 1000 }),
  use(getUpcomingCollections)
);
router.get("/under-review", use(getUnderReviewCollections));
router.get("/search", use(searchCollections));
router.patch("/setsoldout", use(setSoldOut));
router.get(
  "/featured",
  cache({ timeOut: 30 * 1000 }),
  use(getFeaturedCollection)
);
router.get("/phases", use(getPhases));
// router.get("/featured", use(getFeaturedCollection));
router.get("/editions", cache({ timeOut: 30 * 1000 }), use(getEditions));
router.get("/stats", cache({ timeOut: 30 * 1000 }), use(getCollectionStats));
router.get("/edition/:id", cache({ timeOut: 30 * 1000 }), use(getEdition));
router.get("/:id", cache({ timeOut: 30 * 1000 }), getCollection);
router.patch("/:id", auth, use(editCollection));
router.get(
  "/username/:username",
  cache({ timeOut: 30 * 1000 }),
  getCollectionFromUsername
);
router.delete("/:id", auth, deleteCollection);
// router.delete("/", deleteCollections);

module.exports = router;
