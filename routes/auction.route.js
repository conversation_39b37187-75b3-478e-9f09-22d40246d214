const router = require("express").Router();
const cache = require("../middlewares/cache");
const {
  createAuction,
  getAuctionsOfUser,
  placeBid,
  getAuction,
  checkIfNFTExists,
  getAuctions,
  deleteAuction,
  getBidsOfUser,
  getLiveAndUpcomingAuctions,
  getEndedAuctions,
  setAuctionCompleted,
  getFeaturedAuction,
  getUnapprovedAuctionsOfUser,
  getAuctionStats,
} = require("../controllers/auction.controller");
const auth = require("../middlewares/auth");
const use = require("../utils/HOF_promise");

router.get("/nft/exists", use(checkIfNFTExists));
router.get("/user", auth, use(getAuctionsOfUser));
router.get("/bid", use(getBidsOfUser));
router.post("/bid", use(placeBid));
router.get(
  "/upcoming",
  cache({ timeOut: 1 * 60 * 1000 }),
  use(getLiveAndUpcomingAuctions)
);
router.get("/ended", cache({ timeOut: 5 * 60 * 1000 }), use(getEndedAuctions));
router.get(
  "/featured",
  cache({ timeOut: 3 * 60 * 1000 }),
  use(getFeaturedAuction)
);
router.get("/unapproved", auth, use(getUnapprovedAuctionsOfUser));
router.get("/stats", use(getAuctionStats));
router.patch("/:id", use(setAuctionCompleted));
router.post("/", auth, use(createAuction));
router.get("/", use(getAuctions));
router.delete("/:id", auth, use(deleteAuction));
router.get("/:auction_name", use(getAuction));

module.exports = router;
