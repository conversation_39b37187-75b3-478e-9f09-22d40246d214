const router = require("express").Router();
const use = require("../utils/HOF_promise");
const auth = require("../middlewares/auth");
const multer = require("../middlewares/multerMemstore");
const {
  createCollectionDraft,
  getCollectionDraft,
  getCollectionDraftsOfUser,
  getApprovedDrafts,
  editDraft,
  changeDraftImage,
} = require("../controllers/draft.controller");

router.post(
  "/",
  auth,
  multer.fields([
    { name: "image", maxCount: 1 },
    { name: "media2", maxCount: 1 },
  ]),
  use(createCollectionDraft)
);
router.get("/", auth, use(getCollectionDraftsOfUser));
router.get("/approved", use(getApprovedDrafts));
router.patch(
  "/:id",
  auth,
  multer.fields([
    { name: "image", maxCount: 1 },
    { name: "media2", maxCount: 1 },
  ]),
  use(editDraft)
);
router.get("/:id", use(getCollectionDraft));
router.patch("/image/:id", auth, multer.single("image"), use(changeDraftImage));

module.exports = router;
