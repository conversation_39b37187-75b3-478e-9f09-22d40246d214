const router = require("express").Router();
const {
  singleFileUpload,
  folderUpload,
  checkFund,
  addToWhitelist,
  createMetadata,
  createMdata,
  createVideoEdition,
  singleFileUploadWithTurbo,
} = require("../controllers/uploader");
const auth = require("../middlewares/auth");

const use = require("../utils/HOF_promise");
const upload = require("../middlewares/multer");
const multer = require("../middlewares/multerMemstore");
router.post(
  "/singleupload",
  auth,
  upload.single("image"),
  use(singleFileUploadWithTurbo)
);
router.post(
  "/videoedition",
  auth,
  upload.fields([
    { name: "image", maxCount: 1 },
    { name: "video", maxCount: 1 },
  ]),
  use(createVideoEdition)
);
router.post("/csv", auth, upload.single("csv"), use(addToWhitelist));
// router.post("/metadata", auth, upload.single("csv"), use(createMetadata));
router.post("/metadata", auth, upload.single("csv"), use(createMetadata));
router.post(
  "/folderupload",
  auth,
  upload.fields([{ name: "images", maxCount: 10000 }]),
  use(folderUpload)
);
router.get("/fund", auth, use(checkFund));

module.exports = router;
