const router = require("express").Router();
const {
  createFolder,
  updateFolderInfo,
  deleteFolderInfo,
  getFolderInfo,
  getFoldersOfUser,
  getFolderOfUser,
  isCanceledDeployment,
  addTraits,
  updateTraits,
  generateMetadataFromTraits,
  addMetadata,
  getMetadata,
} = require("../controllers/folder.controller");
const auth = require("../middlewares/auth");
const cache = require("../middlewares/cache");
const use = require("../utils/HOF_promise");

router.post("/create", auth, use(createFolder));
router.post("/trait", auth, use(addTraits));
router.post("/trait/genmetadata", auth, use(generateMetadataFromTraits));
router.post("/trait/addMetadata", auth, use(addMetadata));
router.get("/trait", auth, use(getMetadata));
router.get("/one", use(getFolderOfUser));
router.get("/all", auth, use(getFoldersOfUser));
router.get("/check", use(isCanceledDeployment));
router.patch("/trait/:nftId", auth, use(updateTraits));
router.get("/:id", use(getFolderInfo));
router.patch("/:id", auth, use(updateFolderInfo));
router.delete("/:id", auth, use(deleteFolderInfo));

module.exports = router;
