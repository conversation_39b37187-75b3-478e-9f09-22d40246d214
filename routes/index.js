const router = require("express").Router();
const authRoutes = require("./auth.route");
const collectionRoutes = require("./collection.route");
const folderRoutes = require("./folder.route");
const bundlrRoutes = require("./uploader");
const whitelistRoutes = require("./whitelist.route");
const tokenRoutes = require("./token.route");
const verificationRoutes = require("./verification.route");
const auctinRoutes = require("./auction.route");
const draftRoutes = require("./draft.route");
const christmasRoutes = require("../christmas/christmas.route");
const test = require("./test");

router.use("/auth", authRoutes);
router.use("/collection", collectionRoutes);
router.use("/folder", folderRoutes);
router.use("/uploader", bundlrRoutes);
router.use("/whitelist", whitelistRoutes);
router.use("/token", tokenRoutes);
router.use("/verification", verificationRoutes);
router.use("/auction", auctinRoutes);
router.use("/draft", draftRoutes);
router.use("/christmas", christmasRoutes);
router.use("/t", test);

module.exports = router;
