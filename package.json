{"name": "api", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js && node swagger.js", "swagger-autogen": "node ./swagger.js", "dbot": "nodemon ./programs/discord.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"nodemon": "^2.0.20"}, "dependencies": {"@aptos-labs/wallet-adapter-core": "^1.0.1", "@ardrive/turbo-sdk": "^1.30.0", "@google-cloud/storage": "^6.9.5", "@saberhq/token-utils": "^1.14.11", "aptos": "^1.7.1", "aptos-wallet-adapter": "^0.4.4", "async_hooks": "^1.0.0", "axios": "^1.6.8", "bn.js": "^5.2.1", "bull": "^4.14.0", "cors": "^2.8.5", "csv-parse": "^5.3.6", "csv-parser": "^3.0.0", "dotenv": "^16.0.3", "ethereumjs-util": "^7.1.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "keccak256": "^1.0.6", "merkletreejs": "^0.3.9", "mime-types": "^2.1.35", "mongoose": "^6.8.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "redis": "^4.6.6", "socket.io": "^4.6.1", "swagger-autogen": "^2.23.0", "swagger-ui-express": "^4.6.0", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "winston": "^3.8.2"}}