const { default: mongoose } = require("mongoose");
const Whitelist = require("../models/whitelist.model");
const WhitelistEntry = require("../models/whitelistEntries.model");
exports.readWhitelists = async (
  limit,
  skip,
  filter = {},
  sort = {},
  project = {}
) => {
  return await Whitelist.find(filter).sort({ _id: -1 }).limit(limit).skip(skip);
};
exports.countWhitelists = async (filter) => {
  return await Whitelist.countDocuments(filter);
};

exports.readWhitelistEntries = async (filter = {}, phases) => {
  try {
    return await WhitelistEntry.aggregate([
      {
        $match: {
          collection_id: mongoose.Types.ObjectId(filter.collection_id),
        },
      },
      {
        $group: {
          _id: { wallet_address: "$wallet_address", phase: "$phase" },
          mint_limit: { $sum: "$mint_limit" },
        },
      },
      {
        $group: {
          _id: "$_id.wallet_address",
          phases: { $addToSet: "$_id.phase" },
          mint_limit: {
            $sum: {
              $cond: {
                if: { $in: ["$_id.phase", phases] },
                then: "$mint_limit",
                else: 0,
              },
            },
          },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);
    // return await WhitelistEntry.aggregate([
    //   {
    //     $match: {
    //       collection_id: mongoose.Types.ObjectId(filter.collection_id),
    //       phase: { $in: phases }, // Filtering by specific phase(s)
    //     },
    //   },
    // {
    //   $group: {
    //     _id: { wallet_address: "$wallet_address", phase: "$phase" },
    //     mint_limit: { $sum: "$mint_limit" },
    //   },
    // },
    // {
    //   $group: {
    //     _id: "$_id.wallet_address",
    //     phases: { $addToSet: "$_id.phase" },
    //     mint_limit: { $sum: "$mint_limit" },
    //   },
    // },
    // {
    //   $match: {
    //     phases: { $all: phases }, // Ensure all specified phases are included
    //   },
    // },
    // { $sort: { _id: 1 } },
    // ]);
  } catch (error) {
    console.log(error);
  }
};

exports.readWhitelistEntry = async (filter = {}, project = {}) => {
  return await WhitelistEntry.findOne(filter, project);
};

exports.countWhitelistEntries = async (filter = {}) => {
  return await WhitelistEntry.countDocuments(filter);
};

exports.removeWhitelistEntry = async (filter = {}) => {
  return await WhitelistEntry.deleteOne(filter);
};
