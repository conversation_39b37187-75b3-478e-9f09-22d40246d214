const Folder = require("../models/folder.model");

exports.readFolderCount = async (filter = {}) => {
  console.log(filter);
  return await Folder.countDocuments(filter);
};

exports.readFolders = async (
  limit,
  skip,
  filter = {},
  project = {},
  sort = { _id: -1 }
) => {
  // return await Folder.find(filter, project).sort(sort).limit(limit).skip(skip);

  const pipeline = [
    {
      $match: filter,
    },
    {
      $project: project,
    },
    { $sort: sort },
    { $skip: skip },
    { $limit: limit },
  ];
  return await Folder.aggregate(pipeline);
};

exports.readFolder = async (filter = {}, project = {}) => {
  return await Folder.findOne(filter, project);
};

exports.countFolders = async (filter = {}) => {
  return await Folder.countDocuments(filter);
};
