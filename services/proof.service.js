const Proof = require("../models/proof.model");

exports.createOrUpdateProof = async ({
  wallet_address,
  phase,
  collection_id,
  proof,
}) => {
  try {
    await Proof.findOneAndUpdate(
      { collection_id, phase, wallet_address },
      { proof },
      { upsert: true, new: true }
    );
  } catch (error) {
    throw error;
  }
};

exports.getProof = async (data) => {
  const { phase, wallet_address, collection_id } = data;
  try {
    return await Proof.findOne({ phase, wallet_address, collection_id });
  } catch (error) {
    throw error;
  }
};

exports.deleteProofs = async (filter = {}) => {
  try {
    return await Proof.deleteMany(filter);
  } catch (error) {
    throw new Error("Could not delete proofs");
  }
};
