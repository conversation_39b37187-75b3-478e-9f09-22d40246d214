const Collection = require("../models/collection.model");
const Draft = require("../models/draft.model");
exports.readCollections = async (
  limit,
  skip,
  filter = {},
  project = {},
  sort = { _id: -1 }
) => {
  return await Collection.find(filter, project)
    .sort(sort)
    .limit(limit)
    .skip(skip);
};
exports.readCollection = async (filter = {}, project = {}) => {
  return await Collection.findOne(filter, project);
};

exports.countCollections = async (filter = {}) => {
  return await Collection.countDocuments(filter);
};

exports.deleteDraft = async (filter = {}) => {
  return await Draft.findOneAndDelete(filter);
};

exports.updateCollection = async (filter = {}, params = {}) => {
  return await Collection.findOneAndUpdate(
    filter,
    { $set: params },
    { new: true }
  );
};
