const { default: mongoose } = require("mongoose");
const Auction = require("../models/auction.model");

exports.readAuctions = async (
  limit,
  skip,
  filter = {},
  project = {
    auction_name: 1,
    nft: 1,
    min_bid: 1,
    user_id: 1,
    startAt: 1,
    endAt: 1,
    id: 1,
    bid_inc: 1,
    contract: 1,
    coin_type: 1,
    twitter: 1,
    completed: 1,
    instagram: 1,
    biddings: { $last: "$biddings" },
  },
  sort = {}
) => {
  filter.isApproved != undefined ? true : (filter.isApproved = true);
  return await Auction.find(filter, project).sort(sort).limit(limit).skip(skip);
};

exports.readAuction = async (
  filter = {},
  project = {
    auction_name: 1,
    nft: 1,
    min_bid: 1,
    user_id: 1,
    startAt: 1,
    endAt: 1,
    id: 1,
    bid_inc: 1,
    contract: 1,
    coin_type: 1,
    twitter: 1,
    completed: 1,
    instagram: 1,
    biddings: { $sortArray: { input: "$biddings", sortBy: { bid: 1 } } },
  }
) => {
  // filter.isApproved = true;
  return await Auction.findOne(filter, project);
};
exports.countAuctions = async (filter = {}) => {
  filter.isApproved = true;
  return await Auction.countDocuments(filter);
};

exports.placeBidService = async (_id, bid = {}) => {
  return await Auction.findOneAndUpdate(
    { _id },
    { $push: { biddings: bid } },
    {
      returnDocument: "after",
      projection: { biddings: { $slice: -1 } },
    }
  );
};
exports.removeAuction = async (filter, options) => {
  return await Auction.deleteOne(filter, options);
};
exports.isValidBid = async (_id, bid) => {
  const match = await Auction.find({
    _id,
    biddings: {
      $elemMatch: (bid = {
        wallet_address: bid.wallet_address,
        bid: { $gte: bid.bid },
      }),
    },
  });
  if (match.length) return false;
  return true;
};
exports.readBidsOfUser = async (auction_id, wallet_address) => {
  return await Auction.findOne(
    {
      _id: auction_id,
    },
    {
      biddings: {
        $filter: {
          input: "$biddings",
          as: "bid",
          cond: { $eq: ["$$bid.wallet_address", wallet_address] },
        },
      },
    }
  );
};
