const mongoose = require("mongoose");
const whitelistSchema = mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Users",
  },
  collection_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Collections",
  },
  twitter: {
    type: String,
  },
  discord_server_name: {
    type: String,
  },
  discord_server_id: {
    type: String,
  },
  discord_server_url: {
    type: String,
  },
  discord_roles: [],
  whitelist_spots: {
    type: Number,
  },
  whitelist_start: {
    type: Date,
  },
  whitelist_end: {
    type: Date,
  },
});

const Whitelist = mongoose.model("Whitelists", whitelistSchema);
module.exports = Whitelist;
