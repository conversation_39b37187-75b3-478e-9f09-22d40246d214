const mongoose = require("mongoose");
const whitelistEntry = mongoose.Schema({
  collection_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Collections",
  },
  wallet_address: {
    type: String,
  },
  mint_limit: {
    type: Number,
  },
  phase: {
    type: String,
  },
  date: {
    type: Date,
    default: Date.now,
  },
  discord: {
    username: { type: String },
    id: { type: String },
    roles: [String],
  },
  twitter: {
    type: String,
  },
});
const WhitelistEntry = mongoose.model("whitelistEntry", whitelistEntry);
module.exports = WhitelistEntry;
