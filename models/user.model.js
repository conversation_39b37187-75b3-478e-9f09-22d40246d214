const mongoose = require("mongoose");
const jwt = require("jsonwebtoken");

const validateEmail = function (email) {
  var re = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
  return re.test(email);
};
const usersSchema = mongoose.Schema({
  wallet_address: {
    type: String,
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
    unique: true,
    validate: [validateEmail, "Please fill a valid email address"],
  },
  website: {
    type: String,
  },
  twitter: {
    type: String,
  },
  discord: {
    type: String,
  },
  signed: {
    type: Boolean,
    default: false,
  },
  joined_at: {
    type: Date,
    default: Date.now,
  },
  tokens: [
    {
      token: {
        type: String,
        required: true,
      },
    },
  ],
});

usersSchema.methods.generateAuthToken = async function () {
  const user = this;
  const token = jwt.sign({ id: user._id }, process.env.JWT_KEY, {
    expiresIn: "24h",
  });
  user.tokens = user.tokens.concat({ token });
  await user.save();
  return token;
};

const User = mongoose.model("Users", usersSchema);
module.exports = User;
