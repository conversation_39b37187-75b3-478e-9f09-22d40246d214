const mongoose = require("mongoose");

const verificationSchema = mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Users",
  },
  full_name: { type: String },
  image: { type: String },
  dob: { type: Date },
  gender: { type: String },
  occupation: { type: String },
  email: { type: String },
  residential_address: {
    country: { type: String },
    state: { type: String },
    city: { type: String },
    zip_code: { type: String },
  },
  permanent_address: {
    country: { type: String },
    state: { type: String },
    city: { type: String },
    zip_code: { type: String },
  },
  document: {
    type: { type: String },
    documents: [String],
  },
});

const Verification = mongoose.model("verification", verificationSchema);
module.exports = Verification;
