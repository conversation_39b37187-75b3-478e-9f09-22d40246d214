const mongoose = require("mongoose");

const candyMachineSchema = mongoose.Schema({
  candy_id: {
    type: String,
  },
  resource_account: {
    type: String,
  },
  whitelist_sale_time: {
    type: Date,
  },
  public_sale_time: {
    type: Date,
  },
  whitelist_price: {
    type: Number,
  },
  public_sale_price: {
    type: Number,
  },
});

const CandyMachine = mongoose.model("CandyMachine", candyMachineSchema);
module.exports = CandyMachine;
