const mongoose = require("mongoose");

const Collection = mongoose.model(
  "Collections",
  mongoose.Schema({
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Users",
    },
    name: {
      type: String,
    },
    username: {
      type: String,
      default: null,
    },
    description: {
      type: String,
    },
    image: {
      type: String,
    },
    media2: {
      type: String,
    },
    baseURL: {
      type: String,
    },
    supply: {
      type: Number,
    },
    royalty_payee_address: {
      type: String,
    },
    royalty_percentage: {
      type: Number,
    },
    twitter: {
      type: String,
    },
    tweet: {
      type: String,
    },
    instagram: {
      type: String,
    },
    discord: {
      type: String,
    },
    website: {
      type: String,
    },
    candyMachine_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CandyMachine",
    },
    isEdition: {
      type: Boolean,
      default: false,
    },
    edition: {
      type: String,
    },
    created_at: {
      type: Date,
      default: Date.now,
    },
    seed: {
      seedz: {
        type: Boolean,
        default: false,
      },
      coin_type: String,
    },
    candyMachine: {
      candy_id: {
        type: String,
      },
      resource_account: {
        type: String,
      },
      whitelist_sale_time: {
        type: Date,
      },
      public_sale_time: {
        type: Date,
      },
      whitelist_price: {
        type: Number,
      },
      public_sale_price: {
        type: Number,
      },
    },
    isFeatured: {
      type: Boolean,
      default: false,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    isApproved: {
      type: Boolean,
      default: false,
    },
    phases: {
      type: Object,
    },
    status: {
      sold_out: {
        type: Boolean,
        default: false,
      },
      time: {
        type: Date,
      },
    },
    txnhash: {
      type: String,
    },
    collection_id: {
      type: String,
    },
    minted: {
      type: Number,
      default: 0,
    },
    updated_at: {
      type: Date,
    },
  })
);

module.exports = Collection;
