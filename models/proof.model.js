const mongoose = require("mongoose");
const proofSchema = mongoose.Schema(
  {
    collection_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Collections",
    },
    phase: {
      type: String,
    },
    wallet_address: {
      type: String,
    },
    proof: {
      type: String,
    },
  },
  {
    timestamps: {
      createdAt: "created_at",
      updatedAt: "updated_at",
    },
  }
);

proofSchema.index({ phase: 1, wallet_address: 1, collection_id: 1 });

proofSchema.index({ created_at: 1 }, { expireAfterSeconds: 864000 });

const Proof = mongoose.model("proofs", proofSchema);
module.exports = Proof;
