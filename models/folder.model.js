const mongoose = require("mongoose");
const folderSchema = mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Users",
  },
  folder_name: {
    type: String,
    trim: true,
    lowercase: true,
  },
  baseURL: {
    type: String,
  },

  date: {
    type: Date,
    default: Date.now,
  },
  assets: {
    baseURI: { type: String },
    ext: { type: String },
    files: [String],
  },
  metadata: {
    baseURI: { type: String },
    files: [String],
  },
  images: {
    baseURI: { type: String },
    ext: { type: String },
    files: [String],
  },

  traits: [{ nftId: { type: Number, unique: true }, metadata: {} }],
  deployment_failed: {
    type: <PERSON><PERSON>an,
  },
});

const Folder = mongoose.model("Folders", folderSchema);
module.exports = Folder;
