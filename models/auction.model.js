const mongoose = require("mongoose");

const auctionSchema = mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Users",
  },
  auction_name: {
    type: String,
    unique: true,
  },
  nft: {
    type: Object,
  },
  contract: {
    type: String,
  },
  bid_inc: {
    type: Number,
  },
  tweet: {
    type: String,
  },
  startAt: {
    type: Date,
  },
  endAt: {
    type: Date,
  },
  image: {
    type: String,
  },
  min_bid: {
    type: Number,
  },
  id: {
    type: String,
  },
  coin_type: {
    type: String,
  },
  isApproved: {
    type: Boolean,
    default: false,
  },
  isFeatured: {
    type: Boolean,
    default: false,
  },
  completed: {
    type: Boolean,
    default: false,
  },
  twitter: {
    type: String,
  },
  instagram: {
    type: String,
  },
  biddings: [
    {
      wallet_address: {
        type: String,
      },
      bid: {
        type: Number,
      },
      creation_number: {
        type: Number,
      },
      time: {
        type: Date,
      },
    },
  ],
});

const Auction = mongoose.model("auction", auctionSchema);
module.exports = Auction;
