const { parentPort, workerData } = require("worker_threads");
const { MerkleTree } = require("merkletreejs");
const keccak256 = require("keccak256");
const { u64 } = require("@saberhq/token-utils");
const { getCoinDecimals } = require("../utils/getDecimalsFromCoin");
const to_buf = (account, amount, price) => {
  return Buffer.concat([
    Buffer.from(account.slice(2), "hex"),
    new u64(amount).toArrayLike(Buffer, "le", 8),
    new u64(price).toArrayLike(Buffer, "le", 8),
  ]);
};

function findObjectById(array, id) {
  return array.find((item) => item.id === id);
}
const calculateProof = (workerData) => {
  const { whitelistAddresses, wallet_address, phases, phase, coin_type } =
    workerData;
  const decimal = getCoinDecimals(coin_type);
  let WhitelistAddressesBuff = [];

  const currentPhase = findObjectById(phases, phase);
  const currentPhasePrice = Number(currentPhase.mint_price * 10 ** decimal);
  let index, wallet, mint_limit;
  for (let i = 0; i < whitelistAddresses.length; i++) {
    const phaseObj = findObjectById(phases, whitelistAddresses[i].phase);
    const address = whitelistAddresses[i].wallet_address;
    const limit = whitelistAddresses[i].mint_limit;
    const currentWlPhase = whitelistAddresses[i].phase;
    const price = Number(phaseObj.mint_price * 10 ** decimal);
    if (
      !index &&
      wallet_address == address &&
      currentPhasePrice == price &&
      currentWlPhase == phase
    ) {
      index = i;
      mint_limit = limit;
    }
    WhitelistAddressesBuff.push(to_buf(address, limit, price));
  }
  let leafNodes = WhitelistAddressesBuff.map((address) => keccak256(address));
  let tree = new MerkleTree(leafNodes, keccak256, { sortPairs: true });
  const proofs = [];
  const proof = tree.getProof(keccak256(WhitelistAddressesBuff[index]));
  proof.forEach((p) => {
    proofs.push(p.data);
  });

  return JSON.stringify(proofs);
};

parentPort.on("message", (task) => {
  const proofs = calculateProof(task);
  parentPort.postMessage(proofs);
});
