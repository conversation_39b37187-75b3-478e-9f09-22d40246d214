const Folder = require("../models/folder.model");
const User = require("../models/user.model");
const fs = require("fs");

// module.exports = (path_to) => {
//   const fs = require("fs");
//   const buffer = fs.readFileSync(`${path_to}/manifest.arkb`);
//   const json = JSON.parse(buffer.toString());
//   const ids = Object.values(json.paths).map((path) => path.id);
//   return ids;
// };

module.exports = (manifestPath) => {
  try {
    const raw = fs.readFileSync(`${manifestPath}/manifest.json`, "utf-8");
    const cleaned = raw.replace(/^Uploaded folder:\s*/, "");
    const parsed = JSON.parse(cleaned);
    const paths = parsed.manifest.paths;

    const ids = Object.entries(paths)
      .filter(([key]) => key !== "manifest.json")
      .map(([, value]) => value.id);

    const manifestId = parsed.manifestResponse.id;
    const url = `https://arweave.net/${manifestId}/`;

    return { ids, url };
  } catch (error) {
    console.error(error);
    return [];
  }
};
