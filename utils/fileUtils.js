const path = require("path");
const { readdir, stat } = require("fs/promises");

exports.dirSize = async (directory) => {
  const path_to = `uploads/${directory}`;
  const files = await readdir(path_to);
  const stats = files.map((file) => stat(path.join(path_to, file)));

  return (await Promise.all(stats)).reduce(
    (accumulator, { size }) => accumulator + size,
    0
  );
};
exports.formatBytes = function (bytes, decimals = 2) {
  if (!+bytes) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

exports.findCsv = async (filePath) => {
  let csvFile;
  const files = await readdir(filePath);
  for (const file of files) {
    const fileExt = path.extname(file);
    if (fileExt === ".csv") {
      return (csvFile = file);
    }
  }
  return csvFile;
};
