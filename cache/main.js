require("dotenv").config();
const { createClient } = require("redis");
const { NODE_ENV, REDIS_URL } = process.env;

exports.RedisCache = class RedisCache {
  constructor(ttl) {
    this.ttl = ttl;
    this.cache = createClient({
      url: REDIS_URL,
    });
    this.cache.connect();

    this.cache.on("connect", () => {
      console.log(`Redis connection established`);
    });

    this.cache.on("error", (error) => {
      console.error(`Redis error: \n ${error}  `);
    });
  }

  async get(key, fetcher) {
    // bypass cache if not connected to redis.
    if (!this.cache.isReady) {
      return await fetcher();
    }

    return new Promise(async (resolve, reject) => {
      const value = await this.cache.GET(key);
      if (value) {
        // return value if found in cache
        return resolve(JSON.parse(value));
      }
      // if value is not in cache, fetch and return it
      const result = await fetcher();
      await this.cache.set(key, JSON.stringify(result), { EX: this.ttl });
      return resolve(result);
    });
  }
  async set(key, data) {
    await this.cache.set(key, JSON.stringify(data));
  }
  async getWithKey(key) {
    const value = await this.cache.GET(key);
    if (value) {
      return value;
    }
    return false;
  }
  async deleteCache(key) {
    await this.cache.del(key);
  }
  async flush() {
    await this.cache.flushAll();
  }
};
