const { countAuctions } = require("../services/auction.service");

exports.createUniqueName = async (text) => {
  let exists = await countAuctions({ auction_name: text });
  if (exists) {
    let randomInt = Math.floor(Math.random() * 10 + 1);
    const newUsername = text + randomInt;
    return this.createUniqueName(newUsername);
  }
  if (!exists) {
    return text
      .toLowerCase()
      .split(" " || "\t")
      .join("-");
  }
};
