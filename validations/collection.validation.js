const Collection = require("../models/collection.model");
const { countCollections } = require("../services/collection.service");

exports.createUniqueUsername = async (text, name) => {
  let exists = await Collection.countDocuments({ username: text });

  if (text == "" || text == null || text == undefined || text == " ") {
    let words = name.toLowerCase().split(" ");
    return this.createUniqueUsername(words.join("-"));
  }

  if (exists) {
    let randomInt = Math.floor(Math.random() * 10 + 1);
    const newUsername = text + randomInt;
    return this.createUniqueUsername(newUsername);
  }
  if (!exists) return text;
};

exports.txnhashExists = async (txnhash) => {
  return await countCollections({ txnhash });
};
