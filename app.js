const express = require("express");
const app = express();
require("dotenv").config();
const { Server } = require("socket.io");
const http = require("http");
const { spawn } = require("child_process");
const fs = require("fs");
const cron = require("node-cron");

const { PORT, MONGO_URL, BUNDLR_NODE, ORIGIN1, ORIGIN2, ORIGIN3 } = process.env;

const server = http.createServer(app);
const io = new Server(server, {
  pingTimeout: 3600000,
  pingInterval: 25000,
  cors: {
    origin: [ORIGIN1, ORIGIN2, ORIGIN3],
  },
});

const User = require("./models/user.model");
const mongoose = require("mongoose");
const apiRoutes = require("./routes/index");
const cors = require("cors");
const getIds = require("./utils/readArkb");

const swaggerUi = require("swagger-ui-express");
const swaggerDocument = require("./swag/swagger-output.json");
const ErrorHandler = require("./middlewares/error");
// const { postDeploymentOperation } = require("./controllers/folder.controller");
const {
  postDeploymentOperation,
  onDeploymentFailure,
} = require("./controllers/folder.controller");
const { insertAddress } = require("./controllers/whitelist.controller");
const { updateNftMintedCount } = require("./controllers/collection.controller");
const { readFolderCount } = require("./services/folder.service");
const logger = require("./middlewares/logger");
const { deleteOldProofs } = require("./controllers/whitelist");
const initDatabase = require("./db/db");

// connect to mongodb
initDatabase();

if (!fs.existsSync("uploads")) {
  fs.mkdirSync("uploads");
}
// middlewares
app.use(express.json({ limit: "1024mb" }));
app.use(express.urlencoded({ limit: "1024mb", extended: false }));
app.use(cors());

// apis
app.use("/api", apiRoutes);
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocument));
app.use(ErrorHandler);
app.get("/", async (req, res) => {
  try {
    res.send("Helooooo");
  } catch (error) {
    res.send("errorrrrrrrr");
  }
});

io.on("connection", (socket) => {
  console.log("a user connected");

  socket.on("disconnect", () => {
    console.log("user disconnected");
  });

  socket.on("upload", async (user_id, folder_name, folder, type) => {
    const exists = await readFolderCount({ user_id, folder_name });
    const folderOnServer = `uploads/${user_id}`;
    if (!exists || !fs.existsSync(folderOnServer)) {
      console.error("Wrong folder name OR wrong folder not in server!");
      return;
    }

    // const folder = `path/to/user/folder/on/server`;
    // const folder_name = `folder_name as saved on DB`;

    let baseURL;

    // let command = `arkb deploy ${folder} --wallet config/wallet.json --auto-confirm --use-bundler ${BUNDLR_NODE}`;
    let command = `turbo upload-folder -f ${folder} --wallet-file ./config/wallet.json  > ${folder}/manifest.json`;
    console.log(`Running command: ${command}`);

    const cmd = spawn(command, { shell: true });
    cmd.stdout.on("data", (data) => {
      console.log(data.toString());
      socket.emit("output", `${data}`);
      let string = "https://arweave.net/";
      let stringData = data.toString();
      // if (stringData.includes(string)) {
      //   let regexp =
      //     /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?!&//=]*)/gi;
      //   baseURL = `${stringData.match(regexp)[0]}/`;
      // }
    });
    cmd.stderr.on("data", (data) => {
      logger.error(data);
      onDeploymentFailure(user_id, folder_name).then((response) => {
        socket.emit("output", `${data}`);
      });
      console.log("--------stderr-------\n", data.toString());
    });
    cmd.on("close", (code) => {
      console.log("----------------code---------------\n", code);
      socket.emit("output", `Command exited with code ${code}`);
      if (!code) {
        const path = `${folder}/manifest.json`;

        const isDeployed = fs.existsSync(path);
        console.log(isDeployed, "hehre ", path);
        if (isDeployed) {
          /**
           * the postDeploymentOperation function returns a promise
           */
          logger.info(`user: ${user_id}\nfolder: ${folder_name}\n`);
          postDeploymentOperation(type, user_id, folder_name, folder).then(
            (data) => {
              const response = JSON.stringify(data);
              // uncomment THIS LATER
              fs.rmSync(`${folder}`, { recursive: true }) &&
                console.log("Directory removed.");
              socket.emit("post-deployment", `Response: ${response}`);
            }
          );
        }
      }
      // if (code) {
      //   const response = onDeploymentFailure(user_id, folder_name);
      //   console.log(response);
      // }
    });
  });
});
const expressServer = server.listen(PORT, () =>
  logger.info(`Listening on ${PORT}`)
);
// set server timeout
expressServer.setTimeout(3600000);

// cron.schedule("1 2,5,10,15,20 * * *", () => {
//   // [Minute] [Hour] [Day] [Month] [Day of week]
//   /**
//    *  Insert a new wallet address every hour for five times
//    */
//   console.log(`\x1b[33m Scheduler ran at: \n ${new Date()} \x1b[0m`);
//   insertAddress();
// });
// cron.schedule("0 */6 * * *", () => {
//   updateNftMintedCount();
//   console.log("Mint in Collection Updated");
// });
