{"swagger": "2.0", "info": {"title": "Wapal API", "description": "Wapal API documentation", "version": "1.0.0"}, "host": "<PERSON>", "basePath": "/", "tags": [{"name": "Collection", "description": "Collection related APIs."}, {"name": "Folder", "description": "Folder related APIs."}], "schemes": "localhost:3000", "paths": {"/collection/create": {"post": {"tags": ["Collection"], "summary": "Add a new collection to database.", "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"name": {"example": "any"}, "description": {"example": "any"}, "image": {"example": "any"}, "images": {"example": "any"}, "baseURL": {"example": "any"}, "supply": {"example": "any"}, "royalty_payee_address": {"example": "any"}, "royalty_percentage": {"example": "any"}, "whitelist_sale_time": {"example": "any"}, "public_sale_time": {"example": "any"}, "public_sale_price": {"example": "any"}, "whitelist_price": {"example": "any"}, "twitter": {"example": "any"}, "discord": {"example": "any"}, "website": {"example": "any"}, "resource_account": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/collection/all": {"get": {"tags": ["Collection"], "summary": "Get all collections from database.", "description": "", "parameters": [{"name": "limit", "in": "query", "type": "string"}, {"name": "page", "in": "query", "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/collection/{id}": {"get": {"tags": ["Collection"], "summary": "Get a single collection from database.", "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Collection"], "summary": "Delete a single collection from database.", "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/folder/create": {"post": {"description": "", "parameters": [], "responses": {}}}, "/folder/one": {"get": {"description": "", "parameters": [], "responses": {}}}, "/folder/all": {"get": {"description": "", "parameters": [], "responses": {}}}, "/folder/{id}": {"get": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}}, "patch": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}}, "delete": {"description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {}}}, "/bundlr/singleupload": {"post": {"description": "", "parameters": [], "responses": {}}}, "/bundlr/folderupload": {"post": {"description": "", "parameters": [], "responses": {}}}, "/t/test": {"get": {"description": "", "parameters": [], "responses": {}}}}}