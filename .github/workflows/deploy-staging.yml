name: Staging-deploy

on:
  push:
    branches:
      - staging
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
      - name: executing remote ssh commands using ssh key
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USER }}
          passphrase: ${{ secrets.PASSPHRASE }}
          key: ${{ secrets.PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          script: sh deploy-scripts/deploy-staging-backend.sh
