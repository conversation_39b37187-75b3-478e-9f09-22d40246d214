version: "3.1"

services:
  wapal-staging-backend:
    container_name: ${CONTAINER_NAME}
    hostname: backend-server-staging
    build:
      context: ./
      dockerfile: Dockerfile
    restart: always
    user: root:root
    ports:
      - ${PORT}:${PORT}
    volumes:
      - /home/<USER>/testing/testingz/images:/uploads/642ae8995bc2608f0965d101
    networks:
      - staging
    links:
      - redis_st

  redis_st:
    image: redis:latest
    hostname: redis_st
    container_name: redis_st
    ports:
      - 6380:6379
    networks:
     - staging
    restart: always
networks:
  staging:
    
